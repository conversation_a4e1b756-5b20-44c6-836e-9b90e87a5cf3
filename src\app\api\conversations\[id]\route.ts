import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import dbConnect from "@/lib/db/mongodb";
import Conversation from "@/models/Conversation";
import User from "@/models/User";

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get authenticated user session
    const session = await getServerSession();
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const conversationId = params.id;
    
    // Connect to database
    await dbConnect();
    
    // Find conversation by ID
    const conversation = await Conversation.findById(conversationId)
      .populate({
        path: 'participants',
        select: 'name image'
      })
      .populate({
        path: 'matchId',
        select: 'matchScore matchDetails'
      });
    
    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }
    
    // Verify that the requesting user is a participant in this conversation
    if (!conversation.participants.some(p => p._id.toString() === userId)) {
      return NextResponse.json(
        { error: "You are not authorized to view this conversation" },
        { status: 403 }
      );
    }
    
    // Find the other participant (not the current user)
    const otherParticipant = conversation.participants.find(
      participant => participant._id.toString() !== userId
    );
    
    // Mark unread messages as read
    const unreadMessages = conversation.messages.filter(
      message => message.senderId.toString() !== userId && message.readAt === null
    );
    
    if (unreadMessages.length > 0) {
      // Update readAt for all unread messages
      await Conversation.updateOne(
        { _id: conversationId, "messages.senderId": { $ne: userId }, "messages.readAt": null },
        { $set: { "messages.$[elem].readAt": new Date() } },
        { arrayFilters: [{ "elem.senderId": { $ne: userId }, "elem.readAt": null }], multi: true }
      );
    }
    
    // Format conversation data for response
    const conversationData = {
      id: conversation._id,
      matchId: conversation.matchId._id,
      matchScore: conversation.matchId.matchScore,
      matchDetails: conversation.matchId.matchDetails,
      otherUser: {
        id: otherParticipant?._id,
        name: otherParticipant?.name,
        image: otherParticipant?.image
      },
      messages: conversation.messages.map(message => ({
        id: message._id,
        content: message.content,
        senderId: message.senderId,
        isFromCurrentUser: message.senderId.toString() === userId,
        createdAt: message.createdAt,
        readAt: message.readAt
      })),
      createdAt: conversation.createdAt
    };
    
    return NextResponse.json({
      success: true,
      data: conversationData
    });
    
  } catch (error) {
    console.error("Conversation retrieval error:", error);
    return NextResponse.json(
      { error: "Failed to retrieve conversation" },
      { status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get authenticated user session
    const session = await getServerSession();
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const conversationId = params.id;
    
    // Parse request body
    const { content } = await req.json();
    
    // Validate message content
    if (!content || content.trim() === '') {
      return NextResponse.json(
        { error: "Message content cannot be empty" },
        { status: 400 }
      );
    }
    
    // Connect to database
    await dbConnect();
    
    // Find conversation by ID
    const conversation = await Conversation.findById(conversationId);
    
    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }
    
    // Verify that the requesting user is a participant in this conversation
    if (!conversation.participants.some(p => p.toString() === userId)) {
      return NextResponse.json(
        { error: "You are not authorized to send messages in this conversation" },
        { status: 403 }
      );
    }
    
    // Check if user has enough chat credits
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    if (user.subscription.chatCreditsLeft <= 0) {
      return NextResponse.json(
        { error: "You have no chat credits left. Please upgrade your subscription." },
        { status: 403 }
      );
    }
    
    // Create new message
    const newMessage = {
      senderId: userId,
      content,
      readAt: null,
      createdAt: new Date()
    };
    
    // Add message to conversation
    conversation.messages.push(newMessage);
    conversation.lastMessageAt = new Date();
    await conversation.save();
    
    // Decrement user's chat credits
    await User.findByIdAndUpdate(userId, {
      $inc: { 'subscription.chatCreditsLeft': -1 }
    });
    
    // Return the new message
    return NextResponse.json({
      success: true,
      message: "Message sent successfully",
      data: {
        message: {
          id: conversation.messages[conversation.messages.length - 1]._id,
          content,
          senderId: userId,
          isFromCurrentUser: true,
          createdAt: newMessage.createdAt,
          readAt: null
        },
        creditsLeft: user.subscription.chatCreditsLeft - 1
      }
    });
    
  } catch (error) {
    console.error("Message sending error:", error);
    return NextResponse.json(
      { error: "Failed to send message" },
      { status: 500 }
    );
  }
}
