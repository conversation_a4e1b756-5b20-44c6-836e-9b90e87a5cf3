Rekomendasi Pembagian:


Alat Ukur	Domain	Metode
BFI-2-S	Extraversion	Complementary
Agreeableness	Similarity
Conscientiousness	Similarity
Negative Emotionality	Complementary
Open-Mindedness	Similarity
Self Disclosure		Similarity
Rumus Kecocokan:

Similarity:
Kecocokan = 1 - |Skor A - Skor B|
Penjelasan: |Skor A - Skor B| adalah nilai mutlak (absolute value) untuk menghilangkan tanda negatif dan hanya mengambil selisih angka secara positif.
A/B = User yang ingin dihitung kecocokannya.
Complementary:
Kecocokan = 1 - |Skor A - (1 - Skor B)|
Contoh Perhitungan Match Score:

Misal skor kecocokan masing-masing domain: (0.9 + 1.0 + 0.6 + 0.7 + 0.8 + 0.9) ÷ 6
Hasil = 0.8166666667
Dibulatkan = 0.8167
Interpretasi Skor Matching:


Match Score (0–1)	Interpretasi
0.80 – 1.00	🌟 Saling melengkapi kuat
0.60 – 0.79	👍 Potensial bagus
0.40 – 0.59	🤝 Butuh usaha & eksplorasi
< 0.40	⚠️ Cenderung sulit nyambung