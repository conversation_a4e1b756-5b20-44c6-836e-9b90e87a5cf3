import type { Metadata } from "next";
import { Outfit } from "next/font/google";
import "./globals.css";
import Providers from "@/app/components/Providers"; // Impor Providers

const outfit = Outfit({
  subsets: ["latin"],
  display: "swap",
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-outfit",
});

export const metadata: Metadata = {
  title: "Pairsona - Platform Perjodohan Berbasis Psikologi",
  description: "Temukan pasangan hidup yang tepat dengan pendekatan ilmiah dan analisis psikologi mendalam. Bukan sekadar swipe, tapi science.",
  keywords: "perjodohan, psikologi, matchmaking, cinta, hubungan, kompatibilitas, Indonesia",
  authors: [{ name: "Pairsona Team" }],
  openGraph: {
    title: "Pairsona - Platform Perjodohan Berbasis Psikologi",
    description: "Temukan pasangan hidup yang tepat dengan pendekatan ilmiah dan analisis psikologi mendalam.",
    type: "website",
    locale: "id_ID",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="id">
      <body
        className={`${outfit.variable} font-sans antialiased`}
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
