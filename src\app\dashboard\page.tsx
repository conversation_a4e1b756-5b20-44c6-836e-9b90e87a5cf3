"use client";

import { signOut, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useEffect } from "react";

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    console.log("Dashboard useEffect - Status:", status, "Session:", session);

    if (status === "loading") {
      return; // Don't do anything while loading
    }

    if (status === "unauthenticated") {
      console.log("User unauthenticated, redirecting to login");
      router.push("/auth/login");
      return;
    }

    if (status === "authenticated" && session) {
      // Cast session.user to access custom properties like psychTestCompleted
      // Ensure you have defined this property in your session callback in [...nextauth].ts
      const user = session.user as { name?: string | null; email?: string | null; image?: string | null; id?: string | null; gender?: string | null; psychTestCompleted?: boolean | null };

      console.log("Dashboard - User psychTestCompleted:", user?.psychTestCompleted);

      if (user && typeof user.psychTestCompleted === 'boolean' && !user.psychTestCompleted) {
        console.log("Psych test not completed, redirecting to test");
        router.push("/psych-test");
      } else {
        console.log("Psych test completed or undefined, staying on dashboard");
      }
    }
  }, [session, status, router]);

  // Render loading state while session is loading or redirection is about to happen
  if (
    status === "loading" ||
    (status === "authenticated" && session && (session.user as any)?.psychTestCompleted === false) || // Check before rendering dashboard
    status === "unauthenticated"
  ) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <p className="text-xl text-gray-700">Loading...</p>
      </div>
    );
  }

  // If authenticated and psychTestCompleted is true (or not explicitly false, or undefined if not set yet for some reason)
  if (status === "authenticated" && session) {
    // At this point, if psychTestCompleted was false, useEffect would have redirected.
    // So, we can safely render the dashboard content.
    return (
      <div className="min-h-screen bg-gray-100">
        <div className="max-w-6xl mx-auto p-6">
          {/* Header */}
          <div className="bg-white shadow-sm rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="h-16 w-16 bg-[#C9879C] rounded-full flex items-center justify-center">
                  <span className="text-white text-xl font-bold">P</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Welcome, {session.user?.name || session.user?.email}!
                  </h1>
                  <p className="text-gray-600">Find your perfect psychological match</p>
                </div>
              </div>
              <button
                onClick={() => signOut({ callbackUrl: "/" })}
                className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
              >
                Sign Out
              </button>
            </div>
          </div>

          {/* Dashboard Cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Matches Card */}
            <div className="bg-white shadow-sm rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
                 onClick={() => router.push('/dashboard/matches')}>
              <div className="flex items-center space-x-4">
                <div className="h-12 w-12 bg-pink-100 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">💕</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Discover Matches</h3>
                  <p className="text-gray-600 text-sm">Find psychologically compatible people</p>
                </div>
              </div>
            </div>

            {/* Conversations Card */}
            <div className="bg-white shadow-sm rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
                 onClick={() => router.push('/dashboard/conversations')}>
              <div className="flex items-center space-x-4">
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">💬</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Conversations</h3>
                  <p className="text-gray-600 text-sm">Chat with your matches</p>
                </div>
              </div>
            </div>

            {/* Profile Card */}
            <div className="bg-white shadow-sm rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
                 onClick={() => router.push('/dashboard/profile')}>
              <div className="flex items-center space-x-4">
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">👤</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">My Profile</h3>
                  <p className="text-gray-600 text-sm">Edit your profile & photos</p>
                </div>
              </div>
            </div>

            {/* Subscription Card */}
            <div className="bg-white shadow-sm rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
                 onClick={() => router.push('/dashboard/subscription')}>
              <div className="flex items-center space-x-4">
                <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">⭐</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Subscription</h3>
                  <p className="text-gray-600 text-sm">Manage your plan & credits</p>
                </div>
              </div>
            </div>

            {/* Psychology Test Card */}
            <div className="bg-white shadow-sm rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
                 onClick={() => router.push('/psych-test')}>
              <div className="flex items-center space-x-4">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">🧠</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Retake Test</h3>
                  <p className="text-gray-600 text-sm">Update your psychological profile</p>
                </div>
              </div>
            </div>

            {/* Help Card */}
            <div className="bg-white shadow-sm rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-center space-x-4">
                <div className="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">❓</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Help & Support</h3>
                  <p className="text-gray-600 text-sm">Get help with the platform</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Fallback if none of the conditions above are met (e.g., an unexpected state)
  // This also implicitly handles the case where status is "unauthenticated" and redirection is in progress.
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <p className="text-xl text-gray-700">Loading...</p>
    </div>
  );
}
