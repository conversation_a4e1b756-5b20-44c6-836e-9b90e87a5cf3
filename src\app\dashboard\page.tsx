"use client";

import { signOut, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useEffect } from "react";

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    console.log("Dashboard useEffect - Status:", status, "Session:", session);

    if (status === "loading") {
      return; // Don't do anything while loading
    }

    if (status === "unauthenticated") {
      console.log("User unauthenticated, redirecting to login");
      router.push("/auth/login");
      return;
    }

    if (status === "authenticated" && session) {
      // Cast session.user to access custom properties like psychTestCompleted
      // Ensure you have defined this property in your session callback in [...nextauth].ts
      const user = session.user as { name?: string | null; email?: string | null; image?: string | null; id?: string | null; gender?: string | null; psychTestCompleted?: boolean | null };

      console.log("Dashboard - User psychTestCompleted:", user?.psychTestCompleted);

      if (user && typeof user.psychTestCompleted === 'boolean' && !user.psychTestCompleted) {
        console.log("Psych test not completed, redirecting to test");
        router.push("/psych-test");
      } else {
        console.log("Psych test completed or undefined, staying on dashboard");
      }
    }
  }, [session, status, router]);

  // Render loading state while session is loading or redirection is about to happen
  if (
    status === "loading" ||
    (status === "authenticated" && session && (session.user as any)?.psychTestCompleted === false) || // Check before rendering dashboard
    status === "unauthenticated"
  ) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <p className="text-xl text-gray-700">Loading...</p>
      </div>
    );
  }

  // If authenticated and psychTestCompleted is true (or not explicitly false, or undefined if not set yet for some reason)
  if (status === "authenticated" && session) {
    // At this point, if psychTestCompleted was false, useEffect would have redirected.
    // So, we can safely render the dashboard content.
    return (
      <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center">
        <div className="bg-white shadow-md rounded-lg p-8 max-w-md w-full text-center">
          <div className="mx-auto h-20 w-20 mb-6 bg-[#C9879C] rounded-full flex items-center justify-center">
            <span className="text-white text-2xl font-bold">P</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            Selamat Datang di Dashboard, {session.user?.name || session.user?.email}!
          </h1>
          <p className="text-gray-600 mb-8">
            Ini adalah halaman dashboard Anda. Fitur-fitur menarik akan segera hadir di sini.
          </p>
          <button
            onClick={() => signOut({ callbackUrl: "/" })}
            className="w-full bg-[#C9879C] hover:bg-[#b5768c] text-white font-semibold py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-[#C9879C] focus:ring-offset-2 transition duration-150 ease-in-out"
          >
            Keluar
          </button>
        </div>
      </div>
    );
  }

  // Fallback if none of the conditions above are met (e.g., an unexpected state)
  // This also implicitly handles the case where status is "unauthenticated" and redirection is in progress.
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <p className="text-xl text-gray-700">Loading...</p>
    </div>
  );
}
