"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Heart, MessageCircle, User, Star, TrendingUp, Users } from "lucide-react";

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    console.log("Dashboard useEffect - Status:", status, "Session:", session);

    if (status === "loading") {
      return; // Don't do anything while loading
    }

    if (status === "unauthenticated") {
      console.log("User unauthenticated, redirecting to login");
      router.push("/auth/login");
      return;
    }

    if (status === "authenticated" && session) {
      // Cast session.user to access custom properties like psychTestCompleted
      // Ensure you have defined this property in your session callback in [...nextauth].ts
      const user = session.user as { name?: string | null; email?: string | null; image?: string | null; id?: string | null; gender?: string | null; psychTestCompleted?: boolean | null };

      console.log("Dashboard - User psychTestCompleted:", user?.psychTestCompleted);

      if (user && typeof user.psychTestCompleted === 'boolean' && !user.psychTestCompleted) {
        console.log("Psych test not completed, redirecting to test");
        router.push("/psych-test");
      } else {
        console.log("Psych test completed or undefined, staying on dashboard");
      }
    }
  }, [session, status, router]);

  // Render loading state while session is loading or redirection is about to happen
  if (
    status === "loading" ||
    (status === "authenticated" && session && (session.user as any)?.psychTestCompleted === false) || // Check before rendering dashboard
    status === "unauthenticated"
  ) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <p className="text-xl text-gray-700">Loading...</p>
      </div>
    );
  }

  // If authenticated and psychTestCompleted is true (or not explicitly false, or undefined if not set yet for some reason)
  if (status === "authenticated" && session) {
    // At this point, if psychTestCompleted was false, useEffect would have redirected.
    // So, we can safely render the dashboard content.
    return (
      <DashboardLayout>
        <div className="p-6">
          {/* Welcome Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Welcome back, {session.user?.name?.split(' ')[0] || 'User'}! 👋
            </h1>
            <p className="text-gray-600">
              Ready to find your perfect psychological match? Here's your dashboard overview.
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Matches</CardTitle>
                <Heart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  +0 from last week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Chats</CardTitle>
                <MessageCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  +0 new conversations
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Profile Views</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  +0 this month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Match Score</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">--</div>
                <p className="text-xs text-muted-foreground">
                  Average compatibility
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Get started with finding your perfect match
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <button
                  onClick={() => router.push('/dashboard/matches')}
                  className="w-full flex items-center justify-between p-4 bg-[#C9879C] text-white rounded-lg hover:bg-[#A66D80] transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Heart className="h-5 w-5" />
                    <span className="font-medium">Discover New Matches</span>
                  </div>
                  <span className="text-sm opacity-90">→</span>
                </button>

                <button
                  onClick={() => router.push('/dashboard/profile')}
                  className="w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5 text-gray-600" />
                    <span className="font-medium text-gray-900">Complete Your Profile</span>
                  </div>
                  <span className="text-sm text-gray-500">→</span>
                </button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Your Subscription</CardTitle>
                <CardDescription>
                  Manage your plan and see available features
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Current Plan</span>
                    <span className="text-sm text-gray-600">Free</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Chat Credits</span>
                    <span className="text-sm text-gray-600">3 remaining</span>
                  </div>
                  <button
                    onClick={() => router.push('/dashboard/subscription')}
                    className="w-full flex items-center justify-center space-x-2 p-3 bg-yellow-100 text-yellow-800 rounded-lg hover:bg-yellow-200 transition-colors"
                  >
                    <Star className="h-4 w-4" />
                    <span className="font-medium">Upgrade Plan</span>
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Fallback if none of the conditions above are met (e.g., an unexpected state)
  // This also implicitly handles the case where status is "unauthenticated" and redirection is in progress.
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <p className="text-xl text-gray-700">Loading...</p>
    </div>
  );
}
