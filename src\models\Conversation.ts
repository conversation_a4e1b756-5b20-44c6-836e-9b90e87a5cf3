import mongoose, { Schema, Document } from 'mongoose';

export interface IMessage {
  senderId: mongoose.Types.ObjectId;
  content: string;
  readAt: Date | null;
  createdAt: Date;
}

export interface IConversation extends Document {
  participants: mongoose.Types.ObjectId[];
  matchId: mongoose.Types.ObjectId;
  messages: IMessage[];
  lastMessageAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

const MessageSchema = new Schema<IMessage>({
  senderId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  content: { type: String, required: true },
  readAt: { type: Date, default: null },
  createdAt: { type: Date, default: Date.now }
});

const ConversationSchema = new Schema<IConversation>({
  participants: [{ type: Schema.Types.ObjectId, ref: 'User', required: true }],
  matchId: { type: Schema.Types.ObjectId, ref: 'Match', required: true },
  messages: [MessageSchema],
  lastMessageAt: { type: Date, default: Date.now },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// Create indexes for efficient querying
ConversationSchema.index({ participants: 1 });
ConversationSchema.index({ matchId: 1 }, { unique: true });
ConversationSchema.index({ lastMessageAt: -1 }); // For sorting by most recent

export default mongoose.models.Conversation || mongoose.model<IConversation>('Conversation', ConversationSchema);
