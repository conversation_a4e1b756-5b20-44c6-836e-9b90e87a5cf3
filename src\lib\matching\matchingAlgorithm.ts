import { IPsychTest } from '@/models/PsychTest';

/**
 * Calculates similarity score between two values (0-1 scale)
 * Formula: 1 - |A - B|
 */
export function calculateSimilarity(valueA: number, valueB: number): number {
  return 1 - Math.abs(valueA - valueB);
}

/**
 * Calculates complementary score between two values (0-1 scale)
 * Formula: 1 - |A - (1 - B)|
 */
export function calculateComplementary(valueA: number, valueB: number): number {
  return 1 - Math.abs(valueA - (1 - valueB));
}

/**
 * Calculate match score between two users based on their psychological test results
 * Returns an object with overall score and individual domain scores
 */
export function calculateMatchScore(testA: IPsychTest, testB: IPsychTest) {
  // Calculate individual domain scores
  const extraversionScore = calculateComplementary(testA.bfi.extraversion, testB.bfi.extraversion);
  const agreeablenessScore = calculateSimilarity(testA.bfi.agreeableness, testB.bfi.agreeableness);
  const conscientiousnessScore = calculateSimilarity(testA.bfi.conscientiousness, testB.bfi.conscientiousness);
  const negativeEmotionalityScore = calculateComplementary(testA.bfi.negativeEmotionality, testB.bfi.negativeEmotionality);
  const openMindednessScore = calculateSimilarity(testA.bfi.openMindedness, testB.bfi.openMindedness);
  const selfDisclosureScore = calculateSimilarity(testA.selfDisclosure, testB.selfDisclosure);

  // Calculate overall score (average of all domains)
  const domainScores = [
    extraversionScore,
    agreeablenessScore,
    conscientiousnessScore,
    negativeEmotionalityScore,
    openMindednessScore,
    selfDisclosureScore
  ];

  const overallScore = domainScores.reduce((sum, score) => sum + score, 0) / domainScores.length;

  return {
    matchScore: overallScore,
    matchDetails: {
      extraversion: extraversionScore,
      agreeableness: agreeablenessScore,
      conscientiousness: conscientiousnessScore,
      negativeEmotionality: negativeEmotionalityScore,
      openMindedness: openMindednessScore,
      selfDisclosure: selfDisclosureScore
    }
  };
}

/**
 * Get match interpretation based on score
 */
export function getMatchInterpretation(score: number): string {
  if (score >= 0.8) {
    return '🌟 Saling melengkapi kuat';
  } else if (score >= 0.6) {
    return '👍 Potensial bagus';
  } else if (score >= 0.4) {
    return '🤝 Butuh usaha & eksplorasi';
  } else {
    return '🔍 Kecocokan rendah';
  }
}
