import mongoose, { Schema, Document } from 'mongoose';

export interface IUser extends Document {
  name: string;
  email: string;
  emailVerified: Date | null;
  password?: string;
  image?: string;
  dateOfBirth?: Date;
  gender: 'male' | 'female';
  religion?: string;
  occupation?: string;
  isSmoker: boolean;
  acceptDifferentReligion: boolean;
  about?: string;
  photos: string[];
  psychTestCompleted: boolean;
  createdAt: Date;
  updatedAt: Date;
  subscription: {
    plan: 'free' | 'basic' | 'pro' | 'vip';
    startDate?: Date;
    endDate?: Date;
    chatCreditsLeft: number;
    psychologistConsultationLeft: number;
  }
}

const UserSchema = new Schema<IUser>({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  emailVerified: { type: Date, default: null },
  password: { type: String },
  image: { type: String },
  dateOfBirth: { type: Date },
  gender: { type: String, enum: ['male', 'female'], required: true },
  religion: { type: String },
  occupation: { type: String },
  isSmoker: { type: Boolean, default: false },
  acceptDifferentReligion: { type: Boolean, default: false },
  about: { type: String },
  photos: [{ type: String }],
  psychTestCompleted: { type: Boolean, default: false },
  subscription: {
    plan: { type: String, enum: ['free', 'basic', 'pro', 'vip'], default: 'free' },
    startDate: { type: Date },
    endDate: { type: Date },
    chatCreditsLeft: { type: Number, default: 3 }, // Free users get 3 chats
    psychologistConsultationLeft: { type: Number, default: 0 }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// Check if model exists before creating a new one (for Next.js hot reloading)
export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
