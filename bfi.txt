Instruksi Umum:

Mohon item diacak saat ditampilkan dalam Apps.
Menggunakan versi short form (30 item) <PERSON><PERSON> (2017).
32 Item (Respon Likert 1–5):
1 = Sangat Tidak Setuju
2 = Tidak Setuju
3 = Netral
4 = Setuju
5 = Sangat Setuju
Dimensi dan Item:

Extraversion (6 items):

<PERSON><PERSON> ram<PERSON>.
<PERSON>a be<PERSON> tegas.
<PERSON>a penuh energi.
<PERSON>a bukan orang yang menyendiri.
Saya suka menjadi pusat perhatian.
Saya merasa nyaman saat berbicara di depan orang banyak.
Agreeableness (6 items):
7. Saya peduli terhadap perasaan orang lain.
8. <PERSON>a menghormati pandangan orang lain.
9. <PERSON>a memperca<PERSON> orang lain.
10. <PERSON>a mudah berempati.
11. Saya jarang merasa sinis terhadap orang.
12. <PERSON>a suka bekerja sama.

Conscientiousness (6 items):
13. <PERSON>a teratur dan rapi.
14. <PERSON><PERSON> beker<PERSON> keras mencapai target.
15. <PERSON><PERSON> tan<PERSON>ung jawab.
16. <PERSON><PERSON> <PERSON><PERSON>u mempersiapkan diri.
17. <PERSON><PERSON> <PERSON>.
18. <PERSON><PERSON> jar<PERSON>nda-nunda.

Negative Emotionality (6 items):
19. <PERSON><PERSON> mudah merasa cemas.
20. <PERSON>a kadang merasa sedih.
21. Saya cepat marah/emosi.
22. Saya sering merasa khawatir.
23. Saya mudah stres.
24. Saya rentan emosi naik-turun.

Open-Mindedness (6 items):
25. Saya suka belajar hal baru.
26. Saya memiliki imajinasi kreatif.
27. Saya sensitif terhadap keindahan.
28. Saya menikmati ide-ide abstrak.
29. Saya penasaran tentang dunia.
30. Saya berpikiran terbuka.

Item Pembalik (2 items):

Cara penilaian skoring khusus untuk 2 item berikut:
1 = Sangat Setuju
2 = Setuju
3 = Netral
4 = Tidak Setuju
5 = Sangat Tidak Setuju
Saya sering merasa orang lain punya niat tersembunyi.
Saya lebih suka menyendiri daripada berada di tengah keramaian.
Panduan Skoring BFI-2-S:

Skor Domain = Rata-rata dari 6 item terkait.
Skor Tingkat = (Skor domain – 1) ÷ 4.
Reliabilitas: α ≈ .77–.78 (sumber: portal.findresearcher.sdu.dk, researchgate.net, people.umass.edu).
Interpretasi:
≥ 0.75: Tinggi
0.25–0.75: Sedang
≤ 0.25: Rendah
Catatan: Pemetaan dari skor domain untuk matching personality.
Cara Menghitung Skor Tingkat:

Misalnya, skor mentah domain Extraversion = 4.2 (dari rata-rata 6 item BFI-2-S).
Gunakan rumus POMP (Percentage of Maximum Possible):
Skor tingkat = (Skor rata-rata – 1) ÷ 4
Mengapa dikurang 1 dan dibagi 4?
Skala Likert 1–5 → nilai minimum = 1, maksimum = 5
Range = 4 (maks - min)
Contoh:
Skor rata-rata Agreeableness = 3.5
Skor tingkat = (3.5 – 1) ÷ 4 = 0.625 → 62.5%