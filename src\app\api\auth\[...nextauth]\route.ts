import NextAuth, { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";
import { verifyOtp } from "@/lib/fazpass/fazpassClient";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        otp: { label: "OTP", type: "text" },
        otpId: { label: "OTP ID", type: "text" },
      },
      async authorize(credentials) {
        const AUTO_LOGIN_OTP_ID = "POST_REGISTRATION_AUTO_LOGIN";

        if (!credentials?.email || !credentials?.otp) {
          throw new Error('Email dan OTP diperlukan.');
        }
        
        await dbConnect();
        const user = await User.findOne({ email: credentials.email });

        if (!user) {
          throw new Error('Pengguna dengan email ini tidak ditemukan.');
        }

        // Handle auto sign-in after registration
        if (credentials.otpId === AUTO_LOGIN_OTP_ID) {
          if (!user.emailVerified) {
            // This case should ideally be prevented by the verify-otp endpoint ensuring verification first.
            console.error(`Auto-login attempt for ${user.email} failed: email not verified.`);
            throw new Error('Auto-login gagal: Email belum terverifikasi.');
          }
          // User is verified, proceed to create session
          return {
            id: user._id.toString(),
            name: user.name,
            email: user.email,
            image: user.image,
            gender: user.gender,
            psychTestCompleted: user.psychTestCompleted
          };
        }

        // Standard login flow: requires a valid otpId for Fazpass verification
        if (!credentials.otpId) {
          throw new Error('OTP ID diperlukan untuk login standar.');
        }

        try {
          console.log('Standard Login: Verifying OTP with Fazpass:', { otpId: credentials.otpId, otp: credentials.otp });
          const otpVerification = await verifyOtp(credentials.otpId, credentials.otp);

          if (otpVerification.status === true) {
            // Ensure email is marked as verified if not already (e.g., if login is used to verify)
            if (!user.emailVerified) {
              user.emailVerified = new Date();
              await user.save();
            }
            
            return {
              id: user._id.toString(),
              name: user.name,
              email: user.email,
              image: user.image,
              gender: user.gender,
              psychTestCompleted: user.psychTestCompleted
            };
          } else {
            throw new Error(otpVerification.message || 'Kode OTP tidak valid atau telah kedaluwarsa.');
          }
        } catch (error: any) {
          console.error("OTP verification error during standard login:", error);
          throw new Error(error.message || 'Terjadi kesalahan saat verifikasi OTP.');
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user, trigger, session: sessionFromTrigger }) {
      await dbConnect();

      // Scenario 1: Initial sign-in (user object is present from authorize callback)
      if (user?.id) {
        const dbUser = await User.findById(user.id);
        if (!dbUser) {
          throw new Error("User not found in database after authorization.");
        }
        // Populate token with fresh data from DB
        token.id = dbUser._id.toString();
        token.name = dbUser.name;
        token.email = dbUser.email;
        token.gender = dbUser.gender;
        token.psychTestCompleted = dbUser.psychTestCompleted;
        token.image = dbUser.image;
        token.sub = dbUser._id.toString(); // Standard JWT subject claim
      }
      // Scenario 2: Subsequent token use/refresh (user object is not present, but token.id should be)
      else if (token?.id) {
        const dbUser = await User.findById(token.id);
        if (!dbUser) {
          throw new Error("User for token not found. Session invalidated.");
        }
        // Refresh token with latest data from DB
        token.id = dbUser._id.toString(); // Ensure it's string
        token.name = dbUser.name;
        token.email = dbUser.email;
        token.gender = dbUser.gender;
        token.psychTestCompleted = dbUser.psychTestCompleted;
        token.image = dbUser.image;
        token.sub = dbUser._id.toString();
      }
      // If neither user.id nor token.id is present, it's an ambiguous state.
      // NextAuth might call jwt with an empty token initially; it should be populated by user obj if login is successful.

      // Scenario 3: Handle session update trigger from client (e.g., useSession().update())
      if (trigger === "update" && sessionFromTrigger && token?.id) {
        Object.assign(token, sessionFromTrigger); // Apply client-side updates to token

        // Optionally, re-fetch from DB to ensure server-side truth for critical fields after update trigger
        // For example, if psychTestCompleted was part of sessionFromTrigger:
        if (sessionFromTrigger.hasOwnProperty('psychTestCompleted') || sessionFromTrigger.hasOwnProperty('name') || sessionFromTrigger.hasOwnProperty('gender')) {
            const dbUser = await User.findById(token.id);
            if (dbUser) { 
                token.name = dbUser.name;
                token.gender = dbUser.gender;
                token.psychTestCompleted = dbUser.psychTestCompleted;
                token.image = dbUser.image; // Keep other fields consistent
            } else {
                throw new Error("User for token not found during update. Session invalidated.");
            }
        }
      }
      return token; // Return the (potentially modified) token
    },
    async session({ session, token }) {
      // token.id should exist if the jwt callback executed successfully without throwing an error.
      if (session.user && token?.id) { 
        session.user.id = token.id as string;
        session.user.name = token.name as string | null | undefined; // Assuming Session['user']['name'] can be string | null | undefined
        session.user.email = token.email as string | null | undefined; // Assuming Session['user']['email'] can be string | null | undefined
        
        // Ensure gender is a string, assign default if null/undefined from token
        session.user.gender = typeof token.gender === 'string' ? token.gender : ""; // Or handle as per actual type definition for Session['user']['gender']
        
        // Ensure psychTestCompleted is boolean or undefined, map null from token to undefined
        const psychTestCompletedFromToken = token.psychTestCompleted;
        session.user.psychTestCompleted = psychTestCompletedFromToken === null ? undefined : (psychTestCompletedFromToken as boolean | undefined);
        
        session.user.image = token.image as string | null | undefined; // Assuming Session['user']['image'] can be string | null | undefined
      }
      // If token.id is not present or token is otherwise invalid, 
      // the jwt callback should have thrown an error, and NextAuth would invalidate the session.
      // Thus, no need to manually set session.user to undefined here.
      return session;
    },
  },
  pages: {
    signIn: "/auth/login",
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
