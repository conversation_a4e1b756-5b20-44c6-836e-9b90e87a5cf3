import NextAuth from "next-auth";
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";

// Configure NextAuth
const handler = NextAuth({
  providers: [
    CredentialsProvider({
      id: "credentials",
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }
        
        await dbConnect();
        
        // Find user by email
        const user = await User.findOne({ email: credentials.email });
        
        if (!user) {
          throw new Error("No user found with this email");
        }
        
        // Check if password matches
        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password || ""
        );
        
        if (!isPasswordValid) {
          throw new Error("Invalid password");
        }
        
        // Return user object without password
        return {
          id: user._id.toString(),
          name: user.name,
          email: user.email,
          image: user.image,
          emailVerified: user.emailVerified,
          psychTestCompleted: user.psychTestCompleted,
          subscription: user.subscription
        };
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.psychTestCompleted = user.psychTestCompleted;
        token.subscription = user.subscription;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.psychTestCompleted = token.psychTestCompleted as boolean;
        session.user.subscription = token.subscription as {
          plan: 'free' | 'basic' | 'pro' | 'vip';
          chatCreditsLeft: number;
          psychologistConsultationLeft: number;
        };
      }
      return session;
    }
  },
  pages: {
    signIn: "/auth/signin",
    signOut: "/auth/signout",
    error: "/auth/error",
    verifyRequest: "/auth/verify-request",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET || "your-secret-key-change-this-in-production",
});

export { handler as GET, handler as POST };
