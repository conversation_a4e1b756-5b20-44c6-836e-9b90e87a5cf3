"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Image from "next/image";

// BFI-2-S questions (30 items, 6 per dimension) from bfi.txt
const bfiSourceQuestions = [
  // Extraversion
  { id: "BFI_E1", dimension: "extraversion", text: "Saya ramah." },
  { id: "BFI_E2", dimension: "extraversion", text: "Saya bersikap tegas." },
  { id: "BFI_E3", dimension: "extraversion", text: "Saya penuh energi." },
  { id: "BFI_E4", dimension: "extraversion", text: "Saya bukan orang yang menyendiri." },
  { id: "BFI_E5", dimension: "extraversion", text: "Saya suka menjadi pusat perhatian." },
  { id: "BFI_E6", dimension: "extraversion", text: "Saya merasa nyaman saat berbicara di depan orang banyak." },
  // Agreeableness
  { id: "BFI_A1", dimension: "agreeableness", text: "Saya peduli terhadap perasaan orang lain." },
  { id: "BFI_A2", dimension: "agreeableness", text: "Saya menghormati pandangan orang lain." },
  { id: "BFI_A3", dimension: "agreeableness", text: "Saya mempercayai orang lain." },
  { id: "BFI_A4", dimension: "agreeableness", text: "Saya mudah berempati." },
  { id: "BFI_A5", dimension: "agreeableness", text: "Saya jarang merasa sinis terhadap orang." },
  { id: "BFI_A6", dimension: "agreeableness", text: "Saya suka bekerja sama." },
  // Conscientiousness
  { id: "BFI_C1", dimension: "conscientiousness", text: "Saya teratur dan rapi." },
  { id: "BFI_C2", dimension: "conscientiousness", text: "Saya bekerja keras mencapai target." },
  { id: "BFI_C3", dimension: "conscientiousness", text: "Saya tanggung jawab." },
  { id: "BFI_C4", dimension: "conscientiousness", text: "Saya selalu mempersiapkan diri." },
  { id: "BFI_C5", dimension: "conscientiousness", text: "Saya disiplin." },
  { id: "BFI_C6", dimension: "conscientiousness", text: "Saya jarang menunda-nunda." },
  // Negative Emotionality
  { id: "BFI_N1", dimension: "negativeEmotionality", text: "Saya mudah merasa cemas." },
  { id: "BFI_N2", dimension: "negativeEmotionality", text: "Saya kadang merasa sedih." },
  { id: "BFI_N3", dimension: "negativeEmotionality", text: "Saya cepat marah/emosi." },
  { id: "BFI_N4", dimension: "negativeEmotionality", text: "Saya sering merasa khawatir." },
  { id: "BFI_N5", dimension: "negativeEmotionality", text: "Saya mudah stres." },
  { id: "BFI_N6", dimension: "negativeEmotionality", text: "Saya rentan emosi naik-turun." },
  // Open-Mindedness
  { id: "BFI_O1", dimension: "openMindedness", text: "Saya suka belajar hal baru." },
  { id: "BFI_O2", dimension: "openMindedness", text: "Saya memiliki imajinasi kreatif." },
  { id: "BFI_O3", dimension: "openMindedness", text: "Saya sensitif terhadap keindahan." },
  { id: "BFI_O4", dimension: "openMindedness", text: "Saya menikmati ide-ide abstrak." },
  { id: "BFI_O5", dimension: "openMindedness", text: "Saya penasaran tentang dunia." },
  { id: "BFI_O6", dimension: "openMindedness", text: "Saya berpikiran terbuka." },
];

// Self-disclosure questions (20 items) from self.txt
const selfDisclosureSourceQuestions = [
  // Keyakinan & Nilai
  { id: "SD_KN1", text: "Saya sering menyampaikan pandangan religius/pribadi." },
  { id: "SD_KN2", text: "Saya membicarakan pendapat politik." },
  { id: "SD_KN3", text: "Saya menyampaikan nilai-nilai moral." },
  { id: "SD_KN4", text: "Saya berbagi gagasan idealis." },
  // Hubungan Interpersonal
  { id: "SD_HI1", text: "Saya berbagi pengalaman keluarga." },
  { id: "SD_HI2", text: "Saya membicarakan dinamika hubungan pribadi." },
  { id: "SD_HI3", text: "Saya bercerita tentang persahabatan penting." },
  { id: "SD_HI4", text: "Saya berbagi masalah sosial." },
  // Urusan Pribadi
  { id: "SD_UP1", text: "Saya mengungkap pengalaman traumatis." },
  { id: "SD_UP2", text: "Saya bercerita tentang penyakit/masalah kesehatan." },
  { id: "SD_UP3", text: "Saya membuka kerentanan pribadi." },
  { id: "SD_UP4", text: "Saya berbagi kesalahan/penyesalan." },
  // Ketertarikan & Minat
  { id: "SD_KM1", text: "Saya membicarakan hobi besar saya." },
  { id: "SD_KM2", text: "Saya berbagi tentang musik/film favorit." },
  { id: "SD_KM3", text: "Saya bercerita tentang kegiatan kreatif." },
  { id: "SD_KM4", text: "Saya berbagi rencana liburan impian." },
  // Perasaan Intim
  { id: "SD_PI1", text: "Saya mengungkapkan ketakutan terdalam." },
  { id: "SD_PI2", text: "Saya menyatakan harapan/beban emosional." },
  { id: "SD_PI3", text: "Saya mengatakan perasaan cinta/rasa sayang." },
  { id: "SD_PI4", text: "Saya membahas konflik emosional terdalam." },
];

const shuffleArray = (array: any[]) => {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
};

export default function PsychTestPage() {
  const router = useRouter();
  const { update } = useSession();
  const [currentStep, setCurrentStep] = useState(1); // 1: BFI, 2: Self-disclosure
  const [bfiAnswers, setBfiAnswers] = useState<Record<string, number>>({});
  const [selfDisclosureAnswers, setSelfDisclosureAnswers] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [shuffledBfiQuestions, setShuffledBfiQuestions] = useState<typeof bfiSourceQuestions>([]);
  const [shuffledSelfDisclosureQuestions, setShuffledSelfDisclosureQuestions] = useState<typeof selfDisclosureSourceQuestions>([]);

  useEffect(() => {
    setShuffledBfiQuestions(shuffleArray(bfiSourceQuestions));
    setShuffledSelfDisclosureQuestions(shuffleArray(selfDisclosureSourceQuestions));
  }, []);

  const handleBfiAnswer = (questionId: string, value: number) => {
    setBfiAnswers((prev) => ({ ...prev, [questionId]: value }));
  };

  const handleSelfDisclosureAnswer = (questionId: string, value: number) => {
    setSelfDisclosureAnswers((prev) => ({ ...prev, [questionId]: value }));
  };

  const calculateBfiScores = () => {
    const domainScores: Record<string, number[]> = {
      extraversion: [],
      agreeableness: [],
      conscientiousness: [],
      negativeEmotionality: [],
      openMindedness: [],
    };

    shuffledBfiQuestions.forEach((question) => {
      const answer = bfiAnswers[question.id];
      if (answer !== undefined && question.dimension) { // Ensure answer exists and dimension is defined
        domainScores[question.dimension].push(answer);
      }
    });

    const finalScores: Record<string, number> = {};
    for (const dimension in domainScores) {
      const scores = domainScores[dimension];
      if (scores.length > 0) {
        const sum = scores.reduce((acc, val) => acc + val, 0);
        const average = sum / scores.length;
        finalScores[dimension] = (average - 1) / 4; // Normalize: (avg - min_scale) / (max_scale - min_scale)
      } else {
        finalScores[dimension] = 0; // Default if no answers for a dimension
      }
    }
    return finalScores as {
      extraversion: number;
      agreeableness: number;
      conscientiousness: number;
      negativeEmotionality: number;
      openMindedness: number;
    };
  };

  const calculateSelfDisclosureScore = () => {
    let totalScore = 0;
    let count = 0;

    shuffledSelfDisclosureQuestions.forEach((question) => {
      const answer = selfDisclosureAnswers[question.id];
      if (answer !== undefined) { // Ensure answer exists
        totalScore += answer;
        count++;
      }
    });

    if (count === 0) return 0; // Avoid division by zero

    const averageScore = totalScore / count;
    // Normalize: (avg - min_scale) / (max_scale - min_scale)
    // Min scale = 1, Max scale = 5, so (avg - 1) / (5 - 1)
    return (averageScore - 1) / 4;
  };

  const handleNextStep = () => {
    // Check if all BFI questions are answered
    const allBfiAnswered = shuffledBfiQuestions.every((q) => bfiAnswers[q.id] !== undefined);
    
    if (!allBfiAnswered) {
      setError("Harap jawab semua pertanyaan di Bagian 1 sebelum melanjutkan.");
      return;
    }
    
    setError("");
    setCurrentStep(2);
  };

  const handleSubmit = async () => {
    // Check if all self-disclosure questions are answered
    const allSelfDisclosureAnswered = shuffledSelfDisclosureQuestions.every(
      (q) => selfDisclosureAnswers[q.id] !== undefined
    );

    if (!allSelfDisclosureAnswered) {
      setError("Harap jawab semua pertanyaan di Bagian 2 sebelum melanjutkan.");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Calculate scores
      const bfiScores = calculateBfiScores();
      const selfDisclosureScore = calculateSelfDisclosureScore();

      // Submit test results
      const response = await fetch("/api/psych-test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          bfi: bfiScores,
          selfDisclosure: selfDisclosureScore,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to submit test results");
      }

      // Force session refresh by triggering update
      try {
        console.log("Attempting to update session...");
        await update({ psychTestCompleted: true });
        console.log("Session update successful");

        // Force router refresh to ensure fresh data
        router.refresh();

        // Delay to ensure session is updated
        setTimeout(() => {
          console.log("Redirecting to dashboard...");
          router.push("/dashboard");
        }, 500);
      } catch (updateError) {
        // Fallback: force page reload to refresh session
        console.log("Session update failed, using page reload fallback", updateError);
        window.location.href = "/dashboard";
      }
    } catch (err: any) {
      setError(err.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const renderLikertScale = (
    questionId: string,
    selectedValue: number,
    onChange: (id: string, value: number) => void
  ) => {
    return (
      <div className="flex justify-between mt-2">
        {[1, 2, 3, 4, 5].map((value) => (
          <div key={value} className="flex flex-col items-center">
            <button
              type="button"
              onClick={() => onChange(questionId, value)}
              className={`w-8 h-8 rounded-full ${
                selectedValue === value
                  ? "bg-[#C9879C] text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              {value}
            </button>
            <span className="text-xs mt-1 text-center w-20">
              {currentStep === 1 ? (
                value === 1 ? "Sangat Tidak Setuju"
                : value === 2 ? "Tidak Setuju"
                : value === 3 ? "Netral"
                : value === 4 ? "Setuju"
                : "Sangat Setuju"
              ) : (
                value === 1 ? "Sangat Jarang"
                : value === 2 ? "Jarang"
                : value === 3 ? "Netral"
                : value === 4 ? "Sering"
                : "Sangat Sering"
              )}
            </span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-10">
          <div className="mx-auto h-24 w-24 mb-4 bg-[#C9879C] rounded-full flex items-center justify-center">
            <span className="text-white text-3xl font-bold">P</span>
          </div>
          <h1 className="mt-6 text-3xl font-extrabold text-gray-900">
            Tes Psikologi Pairsona
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Jawab pertanyaan berikut untuk menemukan pasangan yang cocok dengan kepribadian Anda
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h2 className="text-lg leading-6 font-medium text-gray-900">
              {currentStep === 1
                ? "Bagian 1: Tes Kepribadian BFI-2-S"
                : "Bagian 2: Tes Self-Disclosure"}
            </h2>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              {currentStep === 1
                ? "Seberapa setuju Anda dengan pernyataan berikut tentang diri Anda?"
                : "Seberapa setuju Anda dengan pernyataan berikut tentang keterbukaan Anda?"}
            </p>
          </div>

          <div className="border-t border-gray-200">
            <div className="px-4 py-5 sm:p-6">
              {currentStep === 1 ? (
                <div className="space-y-8">
                  {shuffledBfiQuestions.map((question, index) => (
                    <div key={question.id} className="border-b pb-6">
                      <div className="flex justify-between items-start">
                        <p className="text-md font-medium text-gray-900">
                          {question.text}
                        </p>
                        <span className="text-sm text-gray-500">{question.id}</span>
                      </div>
                      {renderLikertScale(
                        question.id,
                        bfiAnswers[question.id] || 0,
                        handleBfiAnswer
                      )}
                    </div>
                  ))}

                  <div className="flex justify-end mt-6">
                    <button
                      type="button"
                      onClick={handleNextStep}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#C9879C] hover:bg-[#b77389] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#C9879C]"
                    >
                      Lanjut ke Bagian 2
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-8">
                  {shuffledSelfDisclosureQuestions.map((question, index) => (
                    <div key={question.id} className="border-b pb-6">
                      <div className="flex justify-between items-start">
                        <p className="text-md font-medium text-gray-900">
                          {question.text}
                        </p>
                        <span className="text-sm text-gray-500">{question.id}</span>
                      </div>
                      {renderLikertScale(
                        question.id,
                        selfDisclosureAnswers[question.id] || 0,
                        handleSelfDisclosureAnswer
                      )}
                    </div>
                  ))}

                  <div className="flex justify-between mt-6">
                    <button
                      type="button"
                      onClick={() => setCurrentStep(1)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#C9879C]"
                    >
                      Kembali ke Bagian 1
                    </button>
                    <button
                      type="button"
                      onClick={handleSubmit}
                      disabled={loading}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#C9879C] hover:bg-[#b77389] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#C9879C]"
                    >
                      {loading ? "Memproses..." : "Selesai"}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="mt-6 text-center text-sm text-gray-500">
          <p>
            Tes ini akan membantu kami menemukan pasangan yang cocok dengan kepribadian Anda.
            <br />
            Semua data akan disimpan secara aman dan hanya digunakan untuk tujuan pencocokan.
          </p>
        </div>
      </div>
    </div>
  );
}
