"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, Check, X, MessageCircle, Video, Crown } from "lucide-react";

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  duration: number;
  features: {
    chatCredits: number;
    psychologistConsultation: number;
    unlimitedMatches: boolean;
    prioritySupport: boolean;
    advancedFilters: boolean;
  };
  popular?: boolean;
}

const plans: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    duration: 0,
    features: {
      chatCredits: 3,
      psychologistConsultation: 0,
      unlimitedMatches: false,
      prioritySupport: false,
      advancedFilters: false,
    }
  },
  {
    id: 'basic',
    name: 'Basic',
    price: 99000,
    duration: 30,
    features: {
      chatCredits: 50,
      psychologistConsultation: 0,
      unlimitedMatches: true,
      prioritySupport: false,
      advancedFilters: true,
    }
  },
  {
    id: 'pro',
    name: 'Pro',
    price: 199000,
    duration: 30,
    popular: true,
    features: {
      chatCredits: 200,
      psychologistConsultation: 1,
      unlimitedMatches: true,
      prioritySupport: true,
      advancedFilters: true,
    }
  },
  {
    id: 'vip',
    name: 'VIP',
    price: 299000,
    duration: 30,
    features: {
      chatCredits: 500,
      psychologistConsultation: 3,
      unlimitedMatches: true,
      prioritySupport: true,
      advancedFilters: true,
    }
  }
];

export default function SubscriptionPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [currentPlan, setCurrentPlan] = useState<string>('free');
  const [chatCreditsLeft, setChatCreditsLeft] = useState<number>(0);
  const [consultationLeft, setConsultationLeft] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState<string | null>(null);

  useEffect(() => {
    if (status === "loading") return;
    
    if (status === "unauthenticated") {
      router.push("/auth/login");
      return;
    }

    if (status === "authenticated" && session) {
      const user = session.user as any;
      if (!user.psychTestCompleted) {
        router.push("/psych-test");
        return;
      }
      
      fetchSubscriptionInfo();
    }
  }, [session, status, router]);

  const fetchSubscriptionInfo = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/subscription");
      const data = await response.json();

      if (response.ok) {
        setCurrentPlan(data.data.plan || 'free');
        setChatCreditsLeft(data.data.chatCreditsLeft || 0);
        setConsultationLeft(data.data.psychologistConsultationLeft || 0);
      }
    } catch (err) {
      console.error("Failed to fetch subscription info:", err);
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async (planId: string) => {
    try {
      setPurchasing(planId);
      const response = await fetch("/api/subscription", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to initiate purchase");
      }

      // Redirect to payment page
      if (data.data.paymentUrl) {
        window.location.href = data.data.paymentUrl;
      }
    } catch (err: any) {
      alert(err.message || "Something went wrong");
    } finally {
      setPurchasing(null);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#C9879C] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading subscription info...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Subscription Plans</h1>
          <p className="text-gray-600">
            Choose the perfect plan to enhance your dating experience
          </p>
        </div>

        {/* Current Plan Status */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Your Current Plan</CardTitle>
            <CardDescription>
              Manage your subscription and see remaining credits
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="h-16 w-16 bg-[#C9879C] rounded-full flex items-center justify-center mx-auto mb-2">
                  <Crown className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold text-gray-900 capitalize">{currentPlan} Plan</h3>
                <p className="text-sm text-gray-600">Current subscription</p>
              </div>
              
              <div className="text-center">
                <div className="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <MessageCircle className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900">{chatCreditsLeft}</h3>
                <p className="text-sm text-gray-600">Chat credits remaining</p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Video className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900">{consultationLeft}</h3>
                <p className="text-sm text-gray-600">Consultations remaining</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Subscription Plans */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {plans.map((plan) => (
            <Card 
              key={plan.id} 
              className={`relative ${plan.popular ? 'ring-2 ring-[#C9879C] shadow-lg' : ''} ${
                currentPlan === plan.id ? 'bg-gray-50' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-[#C9879C] text-white border-0">
                    <Star className="h-3 w-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="mt-2">
                  <span className="text-3xl font-bold text-gray-900">
                    {plan.price === 0 ? 'Free' : formatPrice(plan.price)}
                  </span>
                  {plan.duration > 0 && (
                    <span className="text-gray-600">/{plan.duration} days</span>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">
                      {plan.features.chatCredits === 0 ? 'No chat credits' : 
                       plan.features.chatCredits === 500 ? 'Unlimited chats' :
                       `${plan.features.chatCredits} chat credits`}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    {plan.features.psychologistConsultation > 0 ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 text-gray-400" />
                    )}
                    <span className="text-sm">
                      {plan.features.psychologistConsultation === 0 ? 
                        'No consultations' : 
                        `${plan.features.psychologistConsultation} psychologist consultation${plan.features.psychologistConsultation > 1 ? 's' : ''}`}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    {plan.features.unlimitedMatches ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 text-gray-400" />
                    )}
                    <span className="text-sm">Unlimited matches</span>
                  </div>

                  <div className="flex items-center space-x-2">
                    {plan.features.advancedFilters ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 text-gray-400" />
                    )}
                    <span className="text-sm">Advanced filters</span>
                  </div>

                  <div className="flex items-center space-x-2">
                    {plan.features.prioritySupport ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 text-gray-400" />
                    )}
                    <span className="text-sm">Priority support</span>
                  </div>
                </div>

                <div className="pt-4">
                  {currentPlan === plan.id ? (
                    <Button disabled className="w-full" variant="outline">
                      Current Plan
                    </Button>
                  ) : plan.id === 'free' ? (
                    <Button disabled className="w-full" variant="outline">
                      Downgrade Not Available
                    </Button>
                  ) : (
                    <Button
                      onClick={() => handlePurchase(plan.id)}
                      disabled={purchasing === plan.id}
                      className={`w-full ${
                        plan.popular 
                          ? 'bg-[#C9879C] hover:bg-[#A66D80] text-white' 
                          : 'bg-gray-900 hover:bg-gray-800 text-white'
                      }`}
                    >
                      {purchasing === plan.id ? 'Processing...' : 'Upgrade Now'}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* FAQ Section */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Frequently Asked Questions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-1">How do chat credits work?</h4>
              <p className="text-sm text-gray-600">
                Each message you send consumes 1 chat credit. Receiving messages is free.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-1">What happens when my subscription expires?</h4>
              <p className="text-sm text-gray-600">
                You'll be downgraded to the Free plan, but your matches and conversations remain accessible.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-1">Can I cancel my subscription?</h4>
              <p className="text-sm text-gray-600">
                Yes, you can cancel anytime. Your benefits remain active until the end of your billing period.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
