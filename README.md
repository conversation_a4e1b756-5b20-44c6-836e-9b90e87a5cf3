# Pairsona - Platform Perjodohan Berbasis Psikologi

Pairsona adalah platform perjodohan yang menggunakan tes psikologi (BFI-2-S dan Self Disclosure Test) untuk mencocokkan pengguna berdasarkan kecocokan kepribadian mereka.

## Teknologi

Proyek ini dibangun dengan:

- **Frontend & Backend**: [Next.js](https://nextjs.org) dengan App Router
- **Database**: MongoDB
- **Autentikasi**: NextAuth.js dengan Fazpass OTP
- **Styling**: Tailwind CSS
- **Payment Gateway**: Midtrans

## Getting Started

### Prasyarat

- Node.js 18.0.0 atau lebih baru
- MongoDB (lokal atau cloud)
- Akun Fazpass untuk OTP (opsional untuk development)
- Akun Midtrans untuk pembayaran (opsional untuk development)

### Instalasi

1. Clone repositori ini
2. Install dependensi:

```bash
npm install
```

3. Salin file `.env.example` ke `.env.local` dan isi dengan konfigurasi yang sesuai:

```bash
cp .env.example .env.local
```

4. Jalankan development server:

```bash
npm run dev
```

Buka [http://localhost:3000](http://localhost:3000) dengan browser Anda untuk melihat hasilnya.

## Struktur Aplikasi

### Backend

Backend Pairsona diimplementasikan menggunakan Next.js API Routes dengan struktur berikut:

#### Database Models

- `User`: Data profil, informasi langganan, foto
- `PsychTest`: Hasil tes BFI-2-S dan Self Disclosure
- `Match`: Skor dan detail kecocokan antar pengguna
- `Conversation`: Pesan chat antara pengguna yang cocok
- `Transaction`: Catatan pembayaran untuk langganan

#### API Routes

- `/api/auth/*`: Autentikasi dengan verifikasi OTP Fazpass
- `/api/psych-test`: Pengiriman dan pengambilan hasil tes psikologi
- `/api/profile`: Manajemen profil termasuk upload foto
- `/api/matches`: Implementasi algoritma matching
- `/api/conversations`: Sistem chat dengan batasan berbasis langganan
- `/api/subscription`: Integrasi pembayaran dengan Midtrans

### Algoritma Matching

Algoritma matching menggunakan kombinasi skor kesamaan dan komplementer berdasarkan 6 domain psikologis:

1. Extraversion (komplementer)
2. Agreeableness (kesamaan)
3. Conscientiousness (kesamaan)
4. Negative Emotionality (komplementer)
5. Open-Mindedness (kesamaan)
6. Self Disclosure (kesamaan)

Skor kecocokan berkisar dari 0 hingga 1, dengan interpretasi dari kecocokan rendah hingga saling melengkapi kuat.

### Paket Langganan

- **Free**: 3 kredit chat
- **Basic**: 50 kredit chat (Rp 99.000/bulan)
- **Pro**: 200 kredit chat + 1 konsultasi psikolog (Rp 199.000/bulan)
- **VIP**: 500 kredit chat + 3 konsultasi psikolog (Rp 299.000/bulan)

## Deployment

Aplikasi ini dapat di-deploy menggunakan [Vercel](https://vercel.com) atau platform hosting Next.js lainnya. Pastikan untuk mengatur variabel lingkungan yang diperlukan di platform hosting Anda.
