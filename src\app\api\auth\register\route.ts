import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";

// Mock function for Fazpass OTP verification (replace with actual Fazpass API integration)
async function sendFazpassOTP(email: string, name: string) {
  // In production, this would call the Fazpass API to send OTP
  console.log(`Sending OTP to ${email} for user ${name}`);
  
  // Return a mock OTP request ID
  return {
    success: true,
    requestId: `mock-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
    message: "OTP sent successfully"
  };
}

export async function POST(req: NextRequest) {
  try {
    const { name, email, password, gender } = await req.json();
    
    // Validate required fields
    if (!name || !email || !password || !gender) {
      return NextResponse.json(
        { error: "Name, email, password, and gender are required" },
        { status: 400 }
      );
    }
    
    // Validate gender
    if (gender !== 'male' && gender !== 'female') {
      return NextResponse.json(
        { error: "Gender must be either 'male' or 'female'" },
        { status: 400 }
      );
    }
    
    // Connect to database
    await dbConnect();
    
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 }
      );
    }
    
    // Send OTP via Fazpass
    const otpResult = await sendFazpassOTP(email, name);
    if (!otpResult.success) {
      return NextResponse.json(
        { error: "Failed to send OTP" },
        { status: 500 }
      );
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create new user (but don't verify email yet)
    const newUser = new User({
      name,
      email,
      password: hashedPassword,
      gender,
      subscription: {
        plan: 'free',
        chatCreditsLeft: 3,
        psychologistConsultationLeft: 0
      }
    });
    
    await newUser.save();
    
    return NextResponse.json({
      success: true,
      message: "Registration initiated. Please verify your email with the OTP sent.",
      userId: newUser._id,
      requestId: otpResult.requestId
    });
    
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: "Failed to register user" },
      { status: 500 }
    );
  }
}
