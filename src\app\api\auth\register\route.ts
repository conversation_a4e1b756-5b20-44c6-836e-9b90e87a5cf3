import { NextRequest, NextResponse } from "next/server";

import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";
import { requestOtp } from "@/lib/fazpass/fazpassClient";

/**
 * Send OTP via Fazpass API
 * 
 * @param email Email address to send OTP to
 * @param name User's name (for logging purposes)
 * @returns Object with success status and requestId or error message
 */
async function sendFazpassOTP(email: string) {
  try {
    // Request OTP via Fazpass API
    const otpResponse = await requestOtp(email);
    
    console.log(`OTP requested for ${email}: ${otpResponse.data.id}`);
    
    // Return the OTP ID
    return {
      success: true,
      requestId: otpResponse.data.id
    };
  } catch (error) {
    console.error('Failed to request OTP:', error);
    
    // Return an error response
    return {
      success: false,
      error: 'Failed to send verification code. Please try again.'
    };
  }
}

export async function POST(req: NextRequest) {
  try {
    const { name, email, gender } = await req.json();
    
    // Validate required fields
    if (!name || !email || !gender) {
      return NextResponse.json(
        { error: "Name, email, and gender are required" },
        { status: 400 }
      );
    }

    // Validate gender format (optional, but good practice)
    if (gender !== 'male' && gender !== 'female') {
      return NextResponse.json(
        { error: "Gender must be either 'male' or 'female'" },
        { status: 400 }
      );
    }
    
    // Connect to database
    await dbConnect();
    
    // Check if user already exists and is verified
    const existingUser = await User.findOne({ email });
    if (existingUser && existingUser.emailVerified) {
      return NextResponse.json(
        { error: "User with this email already exists and is verified. Please login." },
        { status: 409 }
      );
    }
    
    // Send OTP via Fazpass
    // Jika user ada tapi belum verified, OTP tetap dikirim untuk memungkinkan verifikasi ulang.
    const otpResult = await sendFazpassOTP(email);
    if (!otpResult.success) {
      return NextResponse.json(
        { error: otpResult.error || "Failed to send OTP" },
        { status: 500 }
      );
    }
    
    // User will be created in the verify-otp step
    // Send back all necessary data for the next step
    return NextResponse.json({
      success: true,
      message: "OTP sent to your email. Please verify to complete registration.",
      name: name,
      email: email,
      gender: gender,
      requestId: otpResult.requestId
    });
    
  } catch (error) {
    console.error("Registration initiation error:", error);
    return NextResponse.json(
      { error: "Failed to initiate registration" },
      { status: 500 }
    );
  }
}
