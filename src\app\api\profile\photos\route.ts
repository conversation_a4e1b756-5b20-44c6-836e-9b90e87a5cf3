import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { Session } from "next-auth";
import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";
import digitalOceanClient from "@/lib/storage/digitalOceanClient";

/**
 * Upload photo to DigitalOcean Spaces
 * 
 * @param file File to upload
 * @param userId User ID for file path organization
 * @returns URL of the uploaded file
 */
async function uploadPhotoToStorage(file: File, userId: string): Promise<string> {
  try {
    // Convert File to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Get file extension
    const fileExtension = file.name.substring(file.name.lastIndexOf('.'));
    
    // Generate unique file name
    const fileName = digitalOceanClient.generateFileName(userId, fileExtension);
    
    // Upload to DigitalOcean Spaces
    return await digitalOceanClient.uploadFile(buffer, fileName, file.type);
  } catch (error) {
    console.error('Error uploading file:', error);
    throw new Error('Failed to upload file');
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get authenticated user session
    const session = await getServerSession();
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Parse form data from request
    const formData = await req.formData();
    const photo = formData.get('photo') as File;
    
    if (!photo) {
      return NextResponse.json(
        { error: "No photo provided" },
        { status: 400 }
      );
    }
    
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(photo.type)) {
      return NextResponse.json(
        { error: "Invalid file type. Only JPEG, PNG, and WebP are allowed." },
        { status: 400 }
      );
    }
    
    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (photo.size > maxSize) {
      return NextResponse.json(
        { error: "File too large. Maximum size is 5MB." },
        { status: 400 }
      );
    }
    
    // Upload photo to storage
    const photoUrl = await uploadPhotoToStorage(photo, userId);
    
    // Connect to database
    await dbConnect();
    
    // Update user's photos array
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // Limit to 6 photos maximum
    if (user.photos.length >= 6) {
      return NextResponse.json(
        { error: "Maximum of 6 photos allowed. Delete some photos first." },
        { status: 400 }
      );
    }
    
    // Add new photo URL to user's photos array
    user.photos.push(photoUrl);
    await user.save();
    
    // Return updated photos array
    return NextResponse.json({
      success: true,
      message: "Photo uploaded successfully",
      data: {
        photos: user.photos
      }
    });
    
  } catch (error) {
    console.error("Photo upload error:", error);
    return NextResponse.json(
      { error: "Failed to upload photo" },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    // Get authenticated user session
    const session = await getServerSession();
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Get photo URL from query parameters
    const { searchParams } = new URL(req.url);
    const photoUrl = searchParams.get('url');
    
    if (!photoUrl) {
      return NextResponse.json(
        { error: "Photo URL is required" },
        { status: 400 }
      );
    }
    
    // Connect to database
    await dbConnect();
    
    // Find user and remove the photo URL from their photos array
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // Check if photo exists in user's photos array
    if (!user.photos.includes(photoUrl)) {
      return NextResponse.json(
        { error: "Photo not found in user's photos" },
        { status: 404 }
      );
    }
    
    try {
      // Delete from DigitalOcean Spaces
      await digitalOceanClient.deleteFile(photoUrl);
      
      // Remove photo URL from user's photos array
      user.photos = user.photos.filter((url: string) => url !== photoUrl);
      await user.save();
    } catch (error) {
      console.error('Error deleting photo from storage:', error);
      // Continue with database update even if storage deletion fails
      user.photos = user.photos.filter((url: string) => url !== photoUrl);
      await user.save();
    }
    
    // Return updated photos array
    return NextResponse.json({
      success: true,
      message: "Photo deleted successfully",
      data: {
        photos: user.photos
      }
    });
    
  } catch (error) {
    console.error("Photo deletion error:", error);
    return NextResponse.json(
      { error: "Failed to delete photo" },
      { status: 500 }
    );
  }
}
