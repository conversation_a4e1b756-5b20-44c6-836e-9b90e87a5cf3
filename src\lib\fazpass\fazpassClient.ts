/**
 * Fazpass OTP API Client
 * 
 * Provides functions to interact with Fazpass OTP API for requesting and verifying OTP codes.
 */

/**
 * Request OTP from Fazpass API
 * 
 * @param email Email address to send OTP to
 * @returns Response data including OTP ID
 */
export async function requestOtp(email: string): Promise<FazpassOtpRequestResponse> {
  const apiUrl = process.env.FAZPASS_API_URL + '/v1/otp/request';
  const merchantKey = process.env.FAZPASS_MERCHANT_KEY;
  const gatewayKey = process.env.FAZPASS_GATEWAY_KEY;
  
  if (!merchantKey || !gatewayKey) {
    throw new Error('Fazpass API keys not configured');
  }

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${merchantKey}`
      },
      body: JSON.stringify({
        email,
        gateway_key: gatewayKey
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Fazpass API error: ${errorData.message || response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error requesting OTP:', error);
    throw error;
  }
}

/**
 * Verify OTP with Fazpass API
 * 
 * @param otpId OTP ID received from requestOtp
 * @param otp OTP code entered by user
 * @returns Response data with verification result
 */
export async function verifyOtp(otpId: string, otp: string): Promise<FazpassOtpVerifyResponse> {
  const apiUrl = process.env.FAZPASS_API_URL + '/v1/otp/verify';
  const merchantKey = process.env.FAZPASS_MERCHANT_KEY;
  
  if (!merchantKey) {
    throw new Error('Fazpass API keys not configured');
  }

  console.log('Verifying OTP with Fazpass API:', { otpId, otp });
  
  try {
    const requestBody = {
      otp_id: otpId,
      otp
    };
    console.log('Request body for Fazpass:', requestBody);
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${merchantKey}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Fazpass API error response:', errorData);
      throw new Error(`Fazpass API error: ${errorData.message || response.statusText}`);
    }

    const responseData = await response.json();
    console.log('Fazpass API verify success response:', responseData);
    return responseData;
  } catch (error) {
    console.error('Error during OTP verification process:', error);
    throw error;
  }
}

// Types for Fazpass API responses
export interface FazpassOtpRequestResponse {
  status: boolean;
  message: string;
  code: string;
  data: {
    id: string;
    otp: string;
    otp_length: number;
    channel: string;
    provider: string;
    purpose: string;
  };
}

export interface FazpassOtpVerifyResponse {
  status: boolean;
  message: string;
  code: string;
  data: {
    verified: boolean;
  };
}
