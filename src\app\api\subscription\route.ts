import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";
import Transaction from "@/models/Transaction";

// Mock function for Midtrans Snap integration (replace with actual Midtrans API integration)
async function createMidtransTransaction(userId: string, plan: string, amount: number) {
  // In production, this would call the Midtrans Snap API to create a transaction
  console.log(`Creating Midtrans transaction for user ${userId}, plan ${plan}, amount ${amount}`);
  
  // Generate a unique order ID
  const orderId = `PAIRSONA-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
  
  // Return mock transaction details
  return {
    success: true,
    orderId,
    token: `mock-token-${orderId}`,
    redirectUrl: `https://app.midtrans.com/snap/v3/redirection/${orderId}`,
    expiryTime: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
  };
}

// Subscription plan details
const subscriptionPlans = {
  basic: {
    price: 99000, // IDR
    duration: 30, // days
    chatCredits: 50,
    psychologistConsultation: 0
  },
  pro: {
    price: 199000, // IDR
    duration: 30, // days
    chatCredits: 200,
    psychologistConsultation: 1
  },
  vip: {
    price: 299000, // IDR
    duration: 30, // days
    chatCredits: 500,
    psychologistConsultation: 3
  }
};

export async function GET(req: NextRequest) {
  try {
    // Return subscription plans
    return NextResponse.json({
      success: true,
      data: {
        plans: subscriptionPlans
      }
    });
    
  } catch (error) {
    console.error("Subscription plans retrieval error:", error);
    return NextResponse.json(
      { error: "Failed to retrieve subscription plans" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get authenticated user session
    const session = await getServerSession();
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Parse request body
    const { plan } = await req.json();
    
    // Validate plan
    if (!plan || !['basic', 'pro', 'vip'].includes(plan)) {
      return NextResponse.json(
        { error: "Invalid subscription plan" },
        { status: 400 }
      );
    }
    
    // Get plan details
    const planDetails = subscriptionPlans[plan as keyof typeof subscriptionPlans];
    if (!planDetails) {
      return NextResponse.json(
        { error: "Invalid subscription plan" },
        { status: 400 }
      );
    }
    
    // Connect to database
    await dbConnect();
    
    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // Create Midtrans transaction
    const midtransResult = await createMidtransTransaction(
      userId,
      plan,
      planDetails.price
    );
    
    if (!midtransResult.success) {
      return NextResponse.json(
        { error: "Failed to create payment transaction" },
        { status: 500 }
      );
    }
    
    // Create transaction record
    const transaction = new Transaction({
      userId,
      amount: planDetails.price,
      plan,
      status: 'pending',
      paymentMethod: 'midtrans',
      paymentDetails: {
        orderId: midtransResult.orderId,
        midtransToken: midtransResult.token,
        paymentUrl: midtransResult.redirectUrl
      }
    });
    
    await transaction.save();
    
    // Return payment details
    return NextResponse.json({
      success: true,
      message: "Payment initiated successfully",
      data: {
        transaction: {
          id: transaction._id,
          orderId: midtransResult.orderId,
          amount: planDetails.price,
          plan,
          status: 'pending'
        },
        payment: {
          token: midtransResult.token,
          redirectUrl: midtransResult.redirectUrl,
          expiryTime: midtransResult.expiryTime
        }
      }
    });
    
  } catch (error) {
    console.error("Subscription payment error:", error);
    return NextResponse.json(
      { error: "Failed to process subscription payment" },
      { status: 500 }
    );
  }
}
