import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";
import PsychTest from "@/models/PsychTest";
import Match from "@/models/Match";
import { calculateMatchScore, getMatchInterpretation } from "@/lib/matching/matchingAlgorithm";

export async function GET(req: NextRequest) {
  try {
    // Get authenticated user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status') || 'pending';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    // Validate status
    const validStatuses = ['pending', 'accepted', 'rejected', 'all'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "Invalid status parameter" },
        { status: 400 }
      );
    }
    
    // Connect to database
    await dbConnect();
    
    // Build query
    const query: any = {
      $or: [
        { user1Id: userId },
        { user2Id: userId }
      ]
    };
    
    // Add status filter if not 'all'
    if (status !== 'all') {
      query.status = status;
    }
    
    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Find matches
    const matches = await Match.find(query)
      .sort({ matchScore: -1 })
      .skip(skip)
      .limit(limit)
      .populate('user1Id', 'name image photos gender dateOfBirth occupation')
      .populate('user2Id', 'name image photos gender dateOfBirth occupation');
    
    // Count total matches for pagination
    const totalMatches = await Match.countDocuments(query);
    
    // Process matches to include interpretation and format for client
    const processedMatches = matches.map(match => {
      const isUser1 = match.user1Id._id.toString() === userId;
      const otherUser = isUser1 ? match.user2Id : match.user1Id;
      
      return {
        matchId: match._id,
        matchScore: match.matchScore,
        matchInterpretation: getMatchInterpretation(match.matchScore),
        matchDetails: match.matchDetails,
        status: match.status,
        user: {
          id: otherUser._id,
          name: otherUser.name,
          image: otherUser.image,
          photos: otherUser.photos,
          gender: otherUser.gender,
          dateOfBirth: otherUser.dateOfBirth,
          occupation: otherUser.occupation,
          age: otherUser.dateOfBirth ? 
            Math.floor((new Date().getTime() - new Date(otherUser.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : 
            null
        }
      };
    });
    
    // Return matches with pagination info
    return NextResponse.json({
      success: true,
      data: {
        matches: processedMatches,
        pagination: {
          page,
          limit,
          totalMatches,
          totalPages: Math.ceil(totalMatches / limit)
        }
      }
    });
    
  } catch (error) {
    console.error("Matches retrieval error:", error);
    return NextResponse.json(
      { error: "Failed to retrieve matches" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get authenticated user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Connect to database
    await dbConnect();
    
    // Check if user has completed psych test
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    if (!user.psychTestCompleted) {
      return NextResponse.json(
        { error: "You must complete the psychological tests before matching" },
        { status: 400 }
      );
    }
    
    // Get user's psych test results
    const userTest = await PsychTest.findOne({ userId });
    if (!userTest) {
      return NextResponse.json(
        { error: "Psychological test results not found" },
        { status: 404 }
      );
    }
    
    // Find potential matches (users of opposite gender who have completed psych tests)
    const oppositeGender = user.gender === 'male' ? 'female' : 'male';
    const potentialUsers = await User.find({
      _id: { $ne: userId },
      gender: oppositeGender,
      psychTestCompleted: true
    });
    
    // Get IDs of users already matched with
    const existingMatches = await Match.find({
      $or: [
        { user1Id: userId },
        { user2Id: userId }
      ]
    });
    
    const alreadyMatchedIds = existingMatches.map(match => 
      match.user1Id.toString() === userId ? 
        match.user2Id.toString() : 
        match.user1Id.toString()
    );
    
    // Filter out users already matched with
    const newPotentialUsers = potentialUsers.filter(
      user => !alreadyMatchedIds.includes(user._id.toString())
    );
    
    // If no new potential matches, return empty array
    if (newPotentialUsers.length === 0) {
      return NextResponse.json({
        success: true,
        message: "No new potential matches found",
        data: { matches: [] }
      });
    }
    
    // Calculate match scores for each potential user
    const matchPromises = newPotentialUsers.map(async (potentialUser) => {
      // Get potential user's psych test results
      const potentialUserTest = await PsychTest.findOne({ userId: potentialUser._id });
      if (!potentialUserTest) return null;
      
      // Calculate match score
      const { matchScore, matchDetails } = calculateMatchScore(userTest, potentialUserTest);
      
      // Create match record
      const match = new Match({
        user1Id: userId,
        user2Id: potentialUser._id,
        matchScore,
        matchDetails,
        status: 'pending'
      });
      
      await match.save();
      
      return {
        matchId: match._id,
        matchScore,
        matchInterpretation: getMatchInterpretation(matchScore),
        matchDetails,
        status: 'pending',
        user: {
          id: potentialUser._id,
          name: potentialUser.name,
          image: potentialUser.image,
          photos: potentialUser.photos,
          gender: potentialUser.gender,
          dateOfBirth: potentialUser.dateOfBirth,
          occupation: potentialUser.occupation,
          age: potentialUser.dateOfBirth ? 
            Math.floor((new Date().getTime() - new Date(potentialUser.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : 
            null
        }
      };
    });
    
    // Wait for all match calculations to complete
    const matches = (await Promise.all(matchPromises)).filter(Boolean);
    
    // Sort matches by score (highest first)
    matches.sort((a, b) => {
      if (a && b) {
        return b.matchScore - a.matchScore;
      }
      return 0;
    });
    
    // Return new matches
    return NextResponse.json({
      success: true,
      message: `Found ${matches.length} new potential matches`,
      data: { matches }
    });
    
  } catch (error) {
    console.error("Match generation error:", error);
    return NextResponse.json(
      { error: "Failed to generate matches" },
      { status: 500 }
    );
  }
}
