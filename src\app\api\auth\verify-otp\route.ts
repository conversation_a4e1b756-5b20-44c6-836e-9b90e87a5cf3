import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";
import { verifyOtp } from "@/lib/fazpass/fazpassClient";

/**
 * Verify OTP with Fazpass API
 * 
 * @param otpId OTP ID received from requestOtp
 * @param otp OTP code entered by user
 * @returns Object with verification result
 */
async function verifyFazpassOTP(otpId: string, otp: string) {
  try {
    // Call Fazpass API to verify OTP
    const verifyResponse = await verifyOtp(otpId, otp);
    
    console.log(`OTP verification for ${otpId}: ${verifyResponse.status}`);
    
    return {
      success: verifyResponse.status === true,
      message: verifyResponse.message
    };
  } catch (error) {
    console.error('Failed to verify OTP:', error);
    return {
      success: false,
      message: 'Failed to verify OTP. Please try again.'
    };
  }
}

export async function POST(req: NextRequest) {
  try {
    const { name, email, gender, requestId, otp } = await req.json();
    
    // Validate required fields
    if (!name || !email || !gender || !requestId || !otp) {
      return NextResponse.json(
        { error: "Name, email, gender, request ID, and OTP are required" },
        { status: 400 }
      );
    }
    // Additional validation for gender can be added here if needed, e.g.,
    // if (gender !== 'male' && gender !== 'female') { ... } 
    // but assuming frontend sends correct enum value based on previous setup.
    
    // Verify OTP with Fazpass first
    const verificationResult = await verifyFazpassOTP(requestId, otp);
    if (!verificationResult.success) {
      return NextResponse.json(
        { error: verificationResult.message || "Invalid OTP" },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();
    
    let user = await User.findOne({ email });

    if (user) {
      // User exists
      if (user.emailVerified) {
        return NextResponse.json({
          success: true,
          message: "Email already verified.",
          verified: true
        });
      } else {
        // User exists but not verified, so verify now
        user.emailVerified = new Date();
        await user.save();
        return NextResponse.json({
          success: true,
          message: "Email verified successfully.",
          verified: true,
          userId: user._id // Return userId for potential auto-login or redirection
        });
      }
    } else {
      // User does not exist, create new user
      user = new User({
        name: name, // Use name from request
        email: email,
        gender: gender, // Use gender from request
        emailVerified: new Date(),
        subscription: {
          plan: 'free',
          chatCreditsLeft: 3, // Default credits for new user
          psychologistConsultationLeft: 0
        },
        psychTestCompleted: false // New user hasn't completed the test
      });
      await user.save();
      return NextResponse.json({
        success: true,
        message: "Registration and email verification successful. Please login.",
        verified: true,
        userId: user._id
      });
    }
    
  } catch (error) {
    console.error("OTP verification & user creation error:", error);
    return NextResponse.json(
      { error: "Failed to verify OTP or create user" },
      { status: 500 }
    );
  }
}
