import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";

// Mock function for Fazpass OTP verification (replace with actual Fazpass API integration)
async function verifyFazpassOTP(requestId: string, otp: string) {
  // In production, this would call the Fazpass API to verify OTP
  console.log(`Verifying OTP ${otp} for request ${requestId}`);
  
  // For demo purposes, let's assume OTP "123456" is always valid
  const isValid = otp === "123456";
  
  return {
    success: isValid,
    message: isValid ? "OTP verified successfully" : "Invalid OTP"
  };
}

export async function POST(req: NextRequest) {
  try {
    const { userId, requestId, otp } = await req.json();
    
    // Validate required fields
    if (!userId || !requestId || !otp) {
      return NextResponse.json(
        { error: "User ID, request ID, and OTP are required" },
        { status: 400 }
      );
    }
    
    // Connect to database
    await dbConnect();
    
    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // If user is already verified, return success
    if (user.emailVerified) {
      return NextResponse.json({
        success: true,
        message: "Email already verified",
        verified: true
      });
    }
    
    // Verify OTP with Fazpass
    const verificationResult = await verifyFazpassOTP(requestId, otp);
    if (!verificationResult.success) {
      return NextResponse.json(
        { error: "Invalid OTP" },
        { status: 400 }
      );
    }
    
    // Update user with verified email
    user.emailVerified = new Date();
    await user.save();
    
    return NextResponse.json({
      success: true,
      message: "Email verified successfully",
      verified: true
    });
    
  } catch (error) {
    console.error("OTP verification error:", error);
    return NextResponse.json(
      { error: "Failed to verify OTP" },
      { status: 500 }
    );
  }
}
