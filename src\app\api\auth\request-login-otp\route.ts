import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";
import { requestOtp } from "@/lib/fazpass/fazpassClient";

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    await dbConnect();

    const user = await User.findOne({ email });

    if (!user) {
      return NextResponse.json({ error: "Pengguna dengan email ini tidak ditemukan." }, { status: 404 });
    }

    // Request OTP via Fazpass
    const otpResponse = await requestOtp(email);

    if (!otpResponse.status) {
        // Log the actual error from Fazpass if available
        console.error("Fazpass OTP request failed:", otpResponse.message, otpResponse.data);
        return NextResponse.json({ error: "Gagal mengirimkan OTP. Silakan coba lagi." }, { status: 500 });
    }

    // The OTP is sent by Fazpass, we just confirm it was requested successfully.
    // OTP berhasil dikirim, kembalikan otp_id ke client untuk langkah verifikasi
    return NextResponse.json({ message: "OTP sent successfully", otp_id: otpResponse.data.id }, { status: 200 });

  } catch (error) {
    console.error("Error requesting login OTP:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
