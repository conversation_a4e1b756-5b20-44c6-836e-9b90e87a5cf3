import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from "@aws-sdk/client-s3";

/**
 * DigitalOcean Spaces client for managing user profile photos
 */
class DigitalOceanClient {
  private client: S3Client;
  private bucket: string;
  private baseUrl: string;

  constructor() {
    const endpoint = process.env.DO_SPACES_ENDPOINT;
    const bucket = process.env.DO_SPACES_BUCKET;
    const accessKey = process.env.DO_SPACES_ACCESS_KEY;
    const secretKey = process.env.DO_SPACES_SECRET_KEY;

    if (!endpoint || !bucket || !accessKey || !secretKey) {
      throw new Error("DigitalOcean Spaces configuration is missing");
    }

    this.client = new S3Client({
      endpoint: `https://${endpoint}`,
      region: "us-east-1", // DigitalOcean requires this value, even though the actual region is different
      credentials: {
        accessKeyId: accessKey,
        secretAccessKey: secretKey,
      },
    });

    this.bucket = bucket;
    this.baseUrl = `https://${bucket}.${endpoint}`;
  }

  /**
   * Upload a file to DigitalOcean Spaces
   * 
   * @param file File buffer to upload
   * @param fileName Name to give the file in storage
   * @param contentType MIME type of the file
   * @returns URL of the uploaded file
   */
  async uploadFile(file: Buffer, fileName: string, contentType: string): Promise<string> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: fileName,
        Body: file,
        ContentType: contentType,
        ACL: "public-read", // Make the file publicly accessible
      });

      await this.client.send(command);
      return `${this.baseUrl}/${fileName}`;
    } catch (error) {
      console.error("Error uploading file to DigitalOcean Spaces:", error);
      throw new Error("Failed to upload file");
    }
  }

  /**
   * Delete a file from DigitalOcean Spaces
   * 
   * @param fileName Name of the file to delete
   * @returns True if deletion was successful
   */
  async deleteFile(fileName: string): Promise<boolean> {
    try {
      // Extract the file name from the full URL if needed
      const key = fileName.includes(this.baseUrl)
        ? fileName.replace(`${this.baseUrl}/`, "")
        : fileName;

      const command = new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      await this.client.send(command);
      return true;
    } catch (error) {
      console.error("Error deleting file from DigitalOcean Spaces:", error);
      throw new Error("Failed to delete file");
    }
  }

  /**
   * Generate a unique file name for a user's photo
   * 
   * @param userId User ID
   * @param fileExtension File extension (e.g., .jpg, .png)
   * @returns Unique file name
   */
  generateFileName(userId: string, fileExtension: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 10);
    return `users/${userId}/photos/${timestamp}-${randomString}${fileExtension}`;
  }
}

// Export a singleton instance
const digitalOceanClient = new DigitalOceanClient();
export default digitalOceanClient;
