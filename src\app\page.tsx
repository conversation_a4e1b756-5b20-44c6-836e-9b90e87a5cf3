import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Heart, Brain, Users, Shield, Star, CheckCircle } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Heart className="h-8 w-8 text-[#C9879C]" />
            <span className="text-2xl font-bold text-gray-900">Pairsona</span>
          </div>
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#tentang" className="text-gray-600 hover:text-gray-900 transition-colors">Tentang</a>
            <a href="#fitur" className="text-gray-600 hover:text-gray-900 transition-colors">Fitur</a>
            <a href="#cara-kerja" className="text-gray-600 hover:text-gray-900 transition-colors">Cara Kerja</a>
            <a href="#testimoni" className="text-gray-600 hover:text-gray-900 transition-colors">Testimoni</a>
          </nav>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
              Masuk
            </Button>
            <Button className="bg-[#C9879C] hover:bg-[#A66D80] text-white">
              Daftar Sekarang
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center max-w-4xl">
          <Badge className="mb-6 bg-[#F5E4EA] text-[#A66D80] hover:bg-[#F5E4EA]">
            Platform Perjodohan Berbasis Psikologi
          </Badge>
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Temukan Pasangan Hidup yang
            <span className="text-[#C9879C]"> Tepat</span> dengan
            <span className="text-[#C9879C]"> Ilmu Psikologi</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed">
            Pairsona menggunakan pendekatan ilmiah dan analisis psikologi mendalam untuk mencocokkan Anda
            dengan pasangan yang benar-benar kompatibel. Bukan sekadar swipe, tapi science.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="bg-[#C9879C] hover:bg-[#A66D80] text-white px-8 py-3 text-lg">
              Mulai Tes Psikologi Gratis
            </Button>
            <Button size="lg" variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-3 text-lg">
              Pelajari Lebih Lanjut
            </Button>
          </div>
          <div className="mt-12 flex items-center justify-center space-x-8 text-sm text-gray-500">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span>100% Gratis untuk Memulai</span>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-green-500" />
              <span>Data Aman & Privat</span>
            </div>
            <div className="flex items-center space-x-2">
              <Brain className="h-5 w-5 text-green-500" />
              <span>Berbasis Riset Ilmiah</span>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="tentang" className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Mengapa Pairsona Berbeda?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Kami percaya bahwa cinta sejati dimulai dari kompatibilitas yang tepat.
              Dengan menggunakan BFI-2-S dan Self Disclosure Test, kami membantu
              Anda menemukan pasangan yang benar-benar cocok dengan kepribadian dan tingkat keterbukaan Anda.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg bg-white">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 p-3 bg-[#F5E4EA] rounded-full w-fit">
                  <Brain className="h-8 w-8 text-[#C9879C]" />
                </div>
                <CardTitle className="text-xl text-gray-900">Analisis Psikologi Mendalam</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600 text-center leading-relaxed">
                  Menggunakan BFI-2-S (Big Five Inventory-2-Short) dan Self Disclosure Test
                  untuk menganalisis kepribadian dan keterbukaan diri Anda secara efektif.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 p-3 bg-green-100 rounded-full w-fit">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-xl text-gray-900">Matching Algorithm Canggih</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600 text-center leading-relaxed">
                  Algoritma kami menganalisis hasil BFI-2-S dan Self Disclosure Test
                  untuk menemukan kecocokan dengan tingkat akurasi tinggi berdasarkan riset psikologi terkini.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 p-3 bg-purple-100 rounded-full w-fit">
                  <Shield className="h-8 w-8 text-purple-600" />
                </div>
                <CardTitle className="text-xl text-gray-900">Verifikasi & Keamanan</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600 text-center leading-relaxed">
                  Setiap profil diverifikasi secara manual. Data pribadi Anda dilindungi dengan enkripsi
                  tingkat bank dan kami berkomitmen untuk menjaga privasi Anda dengan standar tertinggi.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="fitur" className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Fitur Unggulan Pairsona
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Platform kami dilengkapi dengan teknologi terdepan dan metodologi psikologi
              yang telah teruji untuk memberikan pengalaman perjodohan yang tak tertandingi.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-6">
              <div className="mx-auto mb-4 p-3 bg-[#F5E4EA] rounded-full w-fit">
                <Brain className="h-6 w-6 text-[#C9879C]" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Tes Kepribadian Komprehensif</h3>
              <p className="text-sm text-gray-600">Analisis efisien dengan BFI-2-S dan Self Disclosure Test</p>
            </div>

            <div className="text-center p-6">
              <div className="mx-auto mb-4 p-3 bg-green-100 rounded-full w-fit">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Matching Score Akurat</h3>
              <p className="text-sm text-gray-600">Skor kompatibilitas berdasarkan hasil BFI-2-S dan Self Disclosure</p>
            </div>

            <div className="text-center p-6">
              <div className="mx-auto mb-4 p-3 bg-purple-100 rounded-full w-fit">
                <Shield className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Profil Terverifikasi</h3>
              <p className="text-sm text-gray-600">Semua anggota melalui proses verifikasi ketat</p>
            </div>

            <div className="text-center p-6">
              <div className="mx-auto mb-4 p-3 bg-orange-100 rounded-full w-fit">
                <Heart className="h-6 w-6 text-orange-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Konseling Hubungan</h3>
              <p className="text-sm text-gray-600">Bimbingan dari psikolog berpengalaman</p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="cara-kerja" className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Bagaimana Pairsona Bekerja?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Proses yang sederhana namun ilmiah untuk menemukan pasangan hidup yang tepat untuk Anda.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="mx-auto mb-6 w-16 h-16 bg-[#C9879C] rounded-full flex items-center justify-center text-white text-2xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Daftar & Tes Psikologi</h3>
              <p className="text-gray-600">
                Lengkapi profil Anda dan ikuti tes BFI-2-S dan Self Disclosure Test yang singkat dan efektif.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-6 w-16 h-16 bg-green-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Analisis Kepribadian</h3>
              <p className="text-gray-600">
                Sistem kami menganalisis hasil tes untuk memahami kepribadian, nilai, dan preferensi Anda secara mendalam.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-6 w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Matching Cerdas</h3>
              <p className="text-gray-600">
                Algoritma AI kami mencocokkan Anda dengan kandidat yang memiliki kompatibilitas tinggi berdasarkan analisis psikologi.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-6 w-16 h-16 bg-orange-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                4
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Komunikasi & Bimbingan</h3>
              <p className="text-gray-600">
                Mulai komunikasi dengan match Anda dan dapatkan bimbingan dari psikolog untuk membangun hubungan yang sehat.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimoni" className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Kisah Sukses Pengguna Pairsona
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Ribuan pasangan telah menemukan cinta sejati melalui pendekatan ilmiah kami.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4 italic">
                  "Pairsona benar-benar mengubah cara saya memandang perjodohan. Tes psikologinya sangat akurat
                  dan saya bertemu dengan pasangan yang benar-benar kompatibel dengan kepribadian saya."
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-[#F5E4EA] rounded-full flex items-center justify-center mr-3">
                    <span className="text-[#C9879C] font-semibold">AS</span>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Andi & Sari</p>
                    <p className="text-sm text-gray-500">Menikah setelah 8 bulan</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4 italic">
                  "Pendekatan ilmiah Pairsona membantu saya memahami diri sendiri lebih baik.
                  Match yang diberikan sangat berkualitas dan sesuai dengan nilai-nilai hidup saya."
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-green-600 font-semibold">RD</span>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Rudi & Dina</p>
                    <p className="text-sm text-gray-500">Bertunangan setelah 6 bulan</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4 italic">
                  "Sebagai orang yang skeptis dengan aplikasi kencan, Pairsona membuktikan bahwa
                  teknologi dan psikologi bisa bekerja sama untuk menciptakan hubungan yang bermakna."
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-purple-600 font-semibold">ML</span>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Maya & Lukman</p>
                    <p className="text-sm text-gray-500">Dalam hubungan serius 1 tahun</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Mengapa Kecocokan Pasangan Sangat Penting?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Data BPS 2024 menunjukkan bahwa ketidakcocokan adalah faktor utama perceraian di Indonesia.
              Pairsona membantu Anda menemukan pasangan yang benar-benar kompatibel dari awal.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="text-center mb-4">
                  <div className="text-4xl font-bold text-[#C9879C] mb-2">62,8%</div>
                  <h3 className="text-xl font-semibold text-gray-900">Perselisihan Terus-Menerus</h3>
                </div>
                <p className="text-gray-600 text-center">
                  Dari 251.125 kasus perceraian di Indonesia disebabkan oleh perselisihan dan pertengkaran terus-menerus yang berakar dari ketidakcocokan kepribadian.
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="text-center mb-4">
                  <div className="text-4xl font-bold text-[#C9879C] mb-2">25,1%</div>
                  <h3 className="text-xl font-semibold text-gray-900">Masalah Ekonomi</h3>
                </div>
                <p className="text-gray-600 text-center">
                  Sebanyak 100.198 kasus perceraian dipicu oleh masalah ekonomi. Tes psikologi kami membantu mengidentifikasi keselarasan dalam nilai dan prioritas finansial.
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="text-center mb-4">
                  <div className="text-4xl font-bold text-[#C9879C] mb-2">1,8%</div>
                  <h3 className="text-xl font-semibold text-gray-900">KDRT</h3>
                </div>
                <p className="text-gray-600 text-center">
                  Kasus KDRT meningkat 4,06% dari tahun sebelumnya. Tes Self Disclosure kami membantu mengidentifikasi tanda-tanda perilaku bermasalah sejak dini.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-12 text-center">
            <p className="text-lg text-gray-700 max-w-3xl mx-auto mb-6">
              Dengan menggunakan BFI-2-S dan Self Disclosure Test, Pairsona membantu Anda menemukan pasangan yang benar-benar kompatibel, mengurangi risiko konflik dan meningkatkan peluang hubungan jangka panjang yang sehat.
            </p>
            <Button className="bg-[#C9879C] hover:bg-[#A66D80] text-white px-6 py-2">
              Mulai Tes Kompatibilitas Sekarang
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-[#C9879C]">
        <div className="container mx-auto text-center max-w-4xl">
          <h2 className="text-4xl font-bold text-white mb-6">
            Siap Menemukan Pasangan Hidup Anda?
          </h2>
          <p className="text-xl text-[#F5E4EA] mb-8 leading-relaxed">
            Bergabunglah dengan ribuan orang yang telah menemukan cinta sejati melalui pendekatan ilmiah kami.
            Mulai perjalanan Anda hari ini, gratis!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="bg-white text-[#C9879C] hover:bg-gray-100 px-8 py-3 text-lg">
              Mulai Tes Psikologi Gratis
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-[#C9879C] px-8 py-3 text-lg">
              Pelajari Lebih Lanjut
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Heart className="h-8 w-8 text-[#DEB1C0]" />
                <span className="text-2xl font-bold">Pairsona</span>
              </div>
              <p className="text-gray-400 mb-4">
                Platform perjodohan berbasis psikologi yang membantu Anda menemukan pasangan hidup yang tepat.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                  <span className="text-sm">FB</span>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                  <span className="text-sm">IG</span>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                  <span className="text-sm">TW</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Platform</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Cara Kerja</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Fitur</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Harga</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Keamanan</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Dukungan</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Pusat Bantuan</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Hubungi Kami</a></li>
                <li><a href="#" className="hover:text-white transition-colors">FAQ</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Legal</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Syarat & Ketentuan</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Kebijakan Privasi</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Cookie Policy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Disclaimer</a></li>
              </ul>
            </div>
          </div>

          <Separator className="my-8 bg-gray-800" />

          <div className="flex flex-col md:flex-row justify-between items-center text-gray-400">
            <p>&copy; 2024 Pairsona. Semua hak dilindungi.</p>
            <p className="mt-4 md:mt-0">Dibuat dengan ❤️ untuk membantu Anda menemukan cinta sejati.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
