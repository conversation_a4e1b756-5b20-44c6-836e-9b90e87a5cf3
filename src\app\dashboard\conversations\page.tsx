"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MessageCircle, User, Clock, Heart } from "lucide-react";

interface Conversation {
  id: string;
  matchId: string;
  otherUser: {
    id: string;
    name: string;
    image?: string;
  };
  lastMessage?: {
    content: string;
    createdAt: string;
    isFromCurrentUser: boolean;
  };
  matchScore: number;
  unreadCount: number;
  createdAt: string;
}

export default function ConversationsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (status === "loading") return;
    
    if (status === "unauthenticated") {
      router.push("/auth/login");
      return;
    }

    if (status === "authenticated" && session) {
      const user = session.user as any;
      if (!user.psychTestCompleted) {
        router.push("/psych-test");
        return;
      }
      
      fetchConversations();
    }
  }, [session, status, router]);

  const fetchConversations = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/conversations");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch conversations");
      }

      setConversations(data.data.conversations || []);
    } catch (err: any) {
      setError(err.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return "Just now";
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#C9879C] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading conversations...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Conversations</h1>
          <p className="text-gray-600">
            Chat with your matches and build meaningful connections
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {/* Conversations List */}
        {conversations.length === 0 ? (
          <div className="text-center py-12">
            <MessageCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No conversations yet</h3>
            <p className="text-gray-600 mb-4">
              Start by accepting matches to begin conversations
            </p>
            <Button 
              onClick={() => router.push('/dashboard/matches')}
              className="bg-[#C9879C] hover:bg-[#A66D80]"
            >
              Find Matches
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {conversations.map((conversation) => (
              <Card 
                key={conversation.id} 
                className="hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => router.push(`/chat/${conversation.id}`)}
              >
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    {/* Avatar */}
                    <div className="relative">
                      <div className="h-12 w-12 bg-gradient-to-br from-[#C9879C] to-[#A66D80] rounded-full flex items-center justify-center">
                        {conversation.otherUser.image ? (
                          <img 
                            src={conversation.otherUser.image} 
                            alt={conversation.otherUser.name}
                            className="w-full h-full object-cover rounded-full"
                          />
                        ) : (
                          <User className="h-6 w-6 text-white" />
                        )}
                      </div>
                      {conversation.unreadCount > 0 && (
                        <div className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center">
                          <span className="text-xs text-white font-medium">
                            {conversation.unreadCount > 9 ? '9+' : conversation.unreadCount}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">
                          {conversation.otherUser.name}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <Badge className="bg-green-100 text-green-800 border-0">
                            <Heart className="h-3 w-3 mr-1" />
                            {Math.round(conversation.matchScore * 100)}% Match
                          </Badge>
                          {conversation.lastMessage && (
                            <span className="text-sm text-gray-500 flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              {formatTime(conversation.lastMessage.createdAt)}
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {conversation.lastMessage ? (
                        <p className="text-gray-600 text-sm truncate">
                          {conversation.lastMessage.isFromCurrentUser && "You: "}
                          {conversation.lastMessage.content}
                        </p>
                      ) : (
                        <p className="text-gray-500 text-sm italic">
                          No messages yet - start the conversation!
                        </p>
                      )}
                    </div>

                    {/* Arrow */}
                    <div className="text-gray-400">
                      <span>→</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
