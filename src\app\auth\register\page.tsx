"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react"; // Import signIn
import Link from "next/link";
import Image from "next/image";

export default function RegisterPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    gender: "",
  });
  // emailForOtp is no longer strictly needed if formData.email is used before clearing, 
  // but let's keep a dedicated state for data to be passed to verifyOtp step for clarity.
  const [dataForOtpVerify, setDataForOtpVerify] = useState({ name: "", email: "", gender: "", requestId: "" });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [otpSent, setOtpSent] = useState(false);
  const [otp, setOtp] = useState("");

  const [requestId, setRequestId] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Registration failed");
      }

      // Store data needed for OTP verification step
      setDataForOtpVerify({
        name: data.name, // Expecting name back from API
        email: data.email,
        gender: data.gender, // Expecting gender back from API
        requestId: data.requestId,
      });
      setOtpSent(true);
    } catch (err: any) {
      setError(err.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await fetch("/api/auth/verify-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: dataForOtpVerify.name,
          email: dataForOtpVerify.email,
          gender: dataForOtpVerify.gender,
          requestId: dataForOtpVerify.requestId,
          otp,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "OTP verification failed");
      }

      // Auto sign-in after successful verification
      const signInResponse = await signIn("credentials", {
        redirect: false,
        email: dataForOtpVerify.email,
        otp: otp, // The OTP user just entered and was verified by our backend
        otpId: "POST_REGISTRATION_AUTO_LOGIN", // Special identifier
      });

      if (signInResponse && signInResponse.ok) {
        router.push("/dashboard"); // Redirect to dashboard on successful auto sign-in
      } else {
        // If auto sign-in fails, redirect to login with a message, 
        // or handle error more specifically
        console.error("Auto sign-in failed:", signInResponse?.error);
        setError(signInResponse?.error || "Gagal masuk otomatis. Silakan coba login manual.");
        // Optionally, still redirect to login page if preferred
        // router.push("/auth/login?registration=success&autologin=failed");
      }
    } catch (err: any) {
      setError(err.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <Image
            src="/logo.png"
            alt="Pairsona Logo"
            width={120}
            height={120}
            className="mx-auto"
          />
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Daftar Akun Baru
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Atau{" "}
            <Link href="/auth/login" className="font-medium text-[#C9879C] hover:text-[#b77389]">
              masuk ke akun yang sudah ada
            </Link>
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {!otpSent ? (
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="rounded-md shadow-sm -space-y-px">
              <div>
                <label htmlFor="name" className="sr-only">
                  Nama Lengkap
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  autoComplete="name"
                  required
                  className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-[#C9879C] focus:border-[#C9879C] focus:z-10 sm:text-sm"
                  placeholder="Nama Lengkap"
                  value={formData.name}
                  onChange={handleChange}
                />
              </div>
              <div>
                <label htmlFor="email" className="sr-only">
                  Email
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#C9879C] focus:border-[#C9879C] focus:z-10 sm:text-sm"
                  placeholder="Email"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
              <div>
                <label htmlFor="gender" className="sr-only">
                  Jenis Kelamin
                </label>
                <select
                  id="gender"
                  name="gender"
                  required
                  className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-[#C9879C] focus:border-[#C9879C] focus:z-10 sm:text-sm"
                  value={formData.gender}
                  onChange={handleChange}
                >
                  <option value="">Pilih Jenis Kelamin</option>
                  <option value="male">Laki-laki</option>
                  <option value="female">Perempuan</option>
                </select>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading || !formData.name || !formData.email || !formData.gender}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#C9879C] hover:bg-[#b77389] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#C9879C] disabled:opacity-50"
              >
                {loading ? "Mengirim OTP..." : "Daftar & Kirim Kode Verifikasi"}
              </button>
            </div>
          </form>
        ) : (
          <form className="mt-8 space-y-6" onSubmit={handleVerifyOtp}>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Verifikasi Email
              </h3>
              <p className="mt-1 text-sm text-gray-600">
                Kode OTP telah dikirim ke email Anda. Masukkan kode tersebut di bawah ini.
              </p>
            </div>
            <div className="rounded-md shadow-sm -space-y-px">
              <div>
                <label htmlFor="otp" className="sr-only">
                  Kode OTP
                </label>
                <input
                  id="otp"
                  name="otp"
                  type="text"
                  required
                  className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#C9879C] focus:border-[#C9879C] focus:z-10 sm:text-sm"
                  placeholder="Masukkan kode OTP"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#C9879C] hover:bg-[#b77389] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#C9879C]"
              >
                {loading ? "Memverifikasi..." : "Verifikasi OTP"}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
