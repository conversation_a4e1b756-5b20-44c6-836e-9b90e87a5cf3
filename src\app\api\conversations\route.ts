import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { Session } from "next-auth";
import dbConnect from "@/lib/db/mongodb";
import Conversation from "@/models/Conversation";
import User from "@/models/User";

export async function GET(req: NextRequest) {
  try {
    // Get authenticated user session
    const session = await getServerSession();
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    // Connect to database
    await dbConnect();
    
    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Find conversations where user is a participant
    const conversations = await Conversation.find({ participants: userId })
      .sort({ lastMessageAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate({
        path: 'participants',
        select: 'name image'
      })
      .populate({
        path: 'matchId',
        select: 'matchScore'
      });
    
    // Count total conversations for pagination
    const totalConversations = await Conversation.countDocuments({ participants: userId });
    
    // Process conversations to format for client
    const processedConversations = conversations.map(conversation => {
      // Find the other participant (not the current user)
      const otherParticipant = conversation.participants.find(
        (participant: any) => participant._id.toString() !== userId
      );
      
      // Get the last message if there are any
      const lastMessage = conversation.messages.length > 0 
        ? conversation.messages[conversation.messages.length - 1] 
        : null;
      
      return {
        id: conversation._id,
        matchId: conversation.matchId._id,
        matchScore: conversation.matchId.matchScore,
        otherUser: {
          id: otherParticipant?._id,
          name: otherParticipant?.name,
          image: otherParticipant?.image
        },
        lastMessage: lastMessage ? {
          content: lastMessage.content,
          senderId: lastMessage.senderId,
          createdAt: lastMessage.createdAt,
          isRead: lastMessage.readAt !== null,
          isFromCurrentUser: lastMessage.senderId.toString() === userId
        } : null,
        lastMessageAt: conversation.lastMessageAt,
        createdAt: conversation.createdAt
      };
    });
    
    // Return conversations with pagination info
    return NextResponse.json({
      success: true,
      data: {
        conversations: processedConversations,
        pagination: {
          page,
          limit,
          totalConversations,
          totalPages: Math.ceil(totalConversations / limit)
        }
      }
    });
    
  } catch (error) {
    console.error("Conversations retrieval error:", error);
    return NextResponse.json(
      { error: "Failed to retrieve conversations" },
      { status: 500 }
    );
  }
}
