"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { User, Camera, Edit, Save, X } from "lucide-react";

interface UserProfile {
  id: string;
  name: string;
  email: string;
  dateOfBirth?: string;
  gender: string;
  occupation?: string;
  religion?: string;
  about?: string;
  photos: string[];
  isSmoker: boolean;
  acceptDifferentReligion: boolean;
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");
  const [editForm, setEditForm] = useState<Partial<UserProfile>>({});

  useEffect(() => {
    if (status === "loading") return;
    
    if (status === "unauthenticated") {
      router.push("/auth/login");
      return;
    }

    if (status === "authenticated" && session) {
      const user = session.user as any;
      if (!user.psychTestCompleted) {
        router.push("/psych-test");
        return;
      }
      
      fetchProfile();
    }
  }, [session, status, router]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/profile");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch profile");
      }

      setProfile(data.data);
      setEditForm(data.data);
    } catch (err: any) {
      setError(err.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      const response = await fetch("/api/profile", {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update profile");
      }

      setProfile(data.data);
      setEditing(false);
      setError("");
    } catch (err: any) {
      setError(err.message || "Something went wrong");
    } finally {
      setSaving(false);
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#C9879C] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading profile...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!profile) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <User className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Profile not found</h3>
            <p className="text-gray-600">Unable to load your profile information.</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">My Profile</h1>
            <p className="text-gray-600">
              Manage your profile information and photos
            </p>
          </div>
          <div className="flex space-x-3">
            {editing ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    setEditing(false);
                    setEditForm(profile);
                    setError("");
                  }}
                  disabled={saving}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={saving}
                  className="bg-[#C9879C] hover:bg-[#A66D80]"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {saving ? "Saving..." : "Save Changes"}
                </Button>
              </>
            ) : (
              <Button
                onClick={() => setEditing(true)}
                className="bg-[#C9879C] hover:bg-[#A66D80]"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            )}
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Profile Photo Section */}
          <Card>
            <CardHeader>
              <CardTitle>Profile Photos</CardTitle>
              <CardDescription>
                Add photos to make your profile more attractive
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Main Photo */}
                <div className="relative">
                  <div className="h-48 w-full bg-gradient-to-br from-[#C9879C] to-[#A66D80] rounded-lg flex items-center justify-center">
                    {profile.photos.length > 0 ? (
                      <img 
                        src={profile.photos[0]} 
                        alt="Profile"
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <User className="h-16 w-16 text-white" />
                    )}
                  </div>
                  <button className="absolute bottom-2 right-2 p-2 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow">
                    <Camera className="h-4 w-4 text-gray-600" />
                  </button>
                </div>

                {/* Additional Photos */}
                <div className="grid grid-cols-3 gap-2">
                  {[1, 2, 3].map((index) => (
                    <div key={index} className="relative">
                      <div className="h-20 w-full bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                        {profile.photos[index] ? (
                          <img 
                            src={profile.photos[index]} 
                            alt={`Photo ${index + 1}`}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <Camera className="h-6 w-6 text-gray-400" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                <Button variant="outline" className="w-full">
                  <Camera className="h-4 w-4 mr-2" />
                  Upload Photos
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Basic Information */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Your basic profile information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  {editing ? (
                    <input
                      type="text"
                      value={editForm.name || ''}
                      onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#C9879C]"
                    />
                  ) : (
                    <p className="text-gray-900">{profile.name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <p className="text-gray-900">{profile.email}</p>
                  <p className="text-xs text-gray-500">Email cannot be changed</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Age
                  </label>
                  <p className="text-gray-900">
                    {profile.dateOfBirth ? `${calculateAge(profile.dateOfBirth)} years old` : 'Not specified'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Gender
                  </label>
                  <p className="text-gray-900 capitalize">{profile.gender}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Occupation
                  </label>
                  {editing ? (
                    <input
                      type="text"
                      value={editForm.occupation || ''}
                      onChange={(e) => setEditForm({...editForm, occupation: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#C9879C]"
                      placeholder="Your occupation"
                    />
                  ) : (
                    <p className="text-gray-900">{profile.occupation || 'Not specified'}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Religion
                  </label>
                  {editing ? (
                    <input
                      type="text"
                      value={editForm.religion || ''}
                      onChange={(e) => setEditForm({...editForm, religion: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#C9879C]"
                      placeholder="Your religion"
                    />
                  ) : (
                    <p className="text-gray-900">{profile.religion || 'Not specified'}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  About Me
                </label>
                {editing ? (
                  <textarea
                    value={editForm.about || ''}
                    onChange={(e) => setEditForm({...editForm, about: e.target.value})}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#C9879C]"
                    placeholder="Tell others about yourself..."
                  />
                ) : (
                  <p className="text-gray-900">{profile.about || 'No description yet'}</p>
                )}
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="isSmoker"
                    checked={editing ? editForm.isSmoker || false : profile.isSmoker}
                    onChange={(e) => editing && setEditForm({...editForm, isSmoker: e.target.checked})}
                    disabled={!editing}
                    className="h-4 w-4 text-[#C9879C] focus:ring-[#C9879C] border-gray-300 rounded"
                  />
                  <label htmlFor="isSmoker" className="text-sm font-medium text-gray-700">
                    I am a smoker
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="acceptDifferentReligion"
                    checked={editing ? editForm.acceptDifferentReligion || false : profile.acceptDifferentReligion}
                    onChange={(e) => editing && setEditForm({...editForm, acceptDifferentReligion: e.target.checked})}
                    disabled={!editing}
                    className="h-4 w-4 text-[#C9879C] focus:ring-[#C9879C] border-gray-300 rounded"
                  />
                  <label htmlFor="acceptDifferentReligion" className="text-sm font-medium text-gray-700">
                    Open to different religions
                  </label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
