"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, X, MessageCircle, User, Calendar, MapPin } from "lucide-react";

interface Match {
  matchId: string;
  user: {
    id: string;
    name: string;
    age?: number;
    location?: string;
    photos: string[];
    about?: string;
    occupation?: string;
    religion?: string;
  };
  matchScore: number;
  matchDetails: {
    extraversion: number;
    agreeableness: number;
    conscientiousness: number;
    negativeEmotionality: number;
    openMindedness: number;
    selfDisclosure: number;
  };
  matchInterpretation: string;
  status: 'pending' | 'accepted' | 'rejected';
}

export default function MatchesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    if (status === "loading") return;
    
    if (status === "unauthenticated") {
      router.push("/auth/login");
      return;
    }

    if (status === "authenticated" && session) {
      const user = session.user as any;
      if (!user.psychTestCompleted) {
        router.push("/psych-test");
        return;
      }
      
      fetchMatches();
    }
  }, [session, status, router]);

  const fetchMatches = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/matches");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch matches");
      }

      setMatches(data.data.matches || []);
    } catch (err: any) {
      setError(err.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const handleMatchAction = async (matchId: string, action: 'accept' | 'reject') => {
    try {
      setActionLoading(matchId);
      const response = await fetch(`/api/matches/${matchId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `Failed to ${action} match`);
      }

      // Update local state
      setMatches(prev => prev.map(match =>
        match.matchId === matchId
          ? { ...match, status: action === 'accept' ? 'accepted' : 'rejected' }
          : match
      ));

      if (action === 'accept') {
        // Show success message or redirect to chat
        alert("Match accepted! You can now start chatting.");
      }
    } catch (err: any) {
      alert(err.message || "Something went wrong");
    } finally {
      setActionLoading(null);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return "text-green-600 bg-green-50";
    if (score >= 0.6) return "text-blue-600 bg-blue-50";
    if (score >= 0.4) return "text-yellow-600 bg-yellow-50";
    return "text-red-600 bg-red-50";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#C9879C] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading matches...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Your Matches</h1>
          <p className="text-gray-600">
            Discover people who are psychologically compatible with you
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {/* Matches Grid */}
        {matches.length === 0 ? (
          <div className="text-center py-12">
            <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No matches yet</h3>
            <p className="text-gray-600 mb-4">
              We're still finding compatible people for you. Check back soon!
            </p>
            <Button 
              onClick={fetchMatches}
              className="bg-[#C9879C] hover:bg-[#A66D80]"
            >
              Refresh Matches
            </Button>
          </div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {matches.map((match) => (
              <Card key={match.matchId} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative">
                  {/* Profile Photo */}
                  <div className="h-64 bg-gradient-to-br from-[#C9879C] to-[#A66D80] flex items-center justify-center">
                    {match.user.photos.length > 0 ? (
                      <img 
                        src={match.user.photos[0]} 
                        alt={match.user.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <User className="h-16 w-16 text-white" />
                    )}
                  </div>
                  
                  {/* Match Score Badge */}
                  <div className="absolute top-4 right-4">
                    <Badge className={`${getScoreColor(match.matchScore)} border-0 font-semibold`}>
                      {Math.round(match.matchScore * 100)}% Match
                    </Badge>
                  </div>
                </div>

                <CardHeader className="pb-3">
                  <CardTitle className="text-xl">{match.user.name}</CardTitle>
                  <CardDescription className="flex items-center gap-4 text-sm">
                    {match.user.age && (
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {match.user.age} years
                      </span>
                    )}
                    {match.user.location && (
                      <span className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        {match.user.location}
                      </span>
                    )}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Compatibility Interpretation */}
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900 mb-1">
                      {match.matchInterpretation}
                    </p>
                    <p className="text-xs text-gray-500">
                      Based on psychological compatibility
                    </p>
                  </div>

                  {/* Basic Info */}
                  {(match.user.occupation || match.user.religion) && (
                    <div className="space-y-1">
                      {match.user.occupation && (
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Work:</span> {match.user.occupation}
                        </p>
                      )}
                      {match.user.religion && (
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Religion:</span> {match.user.religion}
                        </p>
                      )}
                    </div>
                  )}

                  {/* About */}
                  {match.user.about && (
                    <p className="text-sm text-gray-600 line-clamp-3">
                      {match.user.about}
                    </p>
                  )}

                  {/* Action Buttons */}
                  {match.status === 'pending' && (
                    <div className="flex gap-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 border-red-200 text-red-600 hover:bg-red-50"
                        onClick={() => handleMatchAction(match.matchId, 'reject')}
                        disabled={actionLoading === match.matchId}
                      >
                        <X className="h-4 w-4 mr-1" />
                        Pass
                      </Button>
                      <Button
                        size="sm"
                        className="flex-1 bg-[#C9879C] hover:bg-[#A66D80]"
                        onClick={() => handleMatchAction(match.matchId, 'accept')}
                        disabled={actionLoading === match.matchId}
                      >
                        <Heart className="h-4 w-4 mr-1" />
                        Like
                      </Button>
                    </div>
                  )}

                  {match.status === 'accepted' && (
                    <Button
                      size="sm"
                      className="w-full bg-green-600 hover:bg-green-700"
                      onClick={() => router.push(`/chat/${match.matchId}`)}
                    >
                      <MessageCircle className="h-4 w-4 mr-1" />
                      Start Chat
                    </Button>
                  )}

                  {match.status === 'rejected' && (
                    <div className="text-center py-2">
                      <span className="text-sm text-gray-500">Passed</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
