"use client";

import { useState, useEffect } from "react";
import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState("");
  const [otpId, setOtpId] = useState(""); // To store the OTP ID from the server
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [step, setStep] = useState<"email" | "otp">("email");
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const verified = searchParams.get("verified");
    if (verified === "true") {
      setSuccessMessage("Email berhasil diverifikasi! Silakan masukkan email Anda untuk login.");
    }
  }, [searchParams]);

  const handleRequestOtp = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/auth/request-login-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      const data = await res.json();
      if (!res.ok) {
        throw new Error(data.error || 'Gagal mengirim OTP.');
      }
      setOtpId(data.otp_id); // Correctly save the otp_id
      setStep('otp');
      setSuccessMessage(`Kode OTP telah dikirim ke ${email}`);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    const result = await signIn("credentials", {
      redirect: false,
      email,
      otp,
      otpId, // Pass the otpId for verification
    });

    if (result?.error) {
      setError(result.error);
      setLoading(false);
    } else if (result?.ok) {
      const profileRes = await fetch('/api/profile');
      if (profileRes.ok) {
          const profile = await profileRes.json();
          if (profile.psychTestCompleted) {
              router.push('/dashboard');
          } else {
              router.push('/psych-test');
          }
      } else {
          router.push('/psych-test');
      }
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <Image
            className="mx-auto h-24 w-auto"
            src="/logo.png"
            alt="Pairsona"
            width={96}
            height={96}
          />
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {step === 'email' ? 'Masuk ke Akun Anda' : 'Verifikasi Kode OTP'}
          </h2>
        </div>

        {successMessage && !error && <p className="text-sm text-green-600 text-center">{successMessage}</p>}
        {error && <p className="text-sm text-red-600 text-center">{error}</p>}

        {step === "email" ? (
          <form className="mt-8 space-y-6" onSubmit={handleRequestOtp}>
            <div className="rounded-md shadow-sm">
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#C9879C] focus:border-[#C9879C] sm:text-sm"
                placeholder="Alamat Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#C9879C] hover:bg-[#b5768c] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#C9879C] disabled:bg-gray-400"
            >
              {loading ? "Mengirim..." : "Kirim Kode OTP"}
            </button>
          </form>
        ) : (
          <form className="mt-8 space-y-6" onSubmit={handleLogin}>
             <div className="rounded-md shadow-sm">
              <input
                id="otp"
                name="otp"
                type="text"
                inputMode="numeric"
                autoComplete="one-time-code"
                required
                className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#C9879C] focus:border-[#C9879C] sm:text-sm"
                placeholder="Kode OTP"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
              />
            </div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#C9879C] hover:bg-[#b5768c] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#C9879C] disabled:bg-gray-400"
            >
              {loading ? "Memverifikasi..." : "Masuk"}
            </button>
            <div className="text-sm text-center">
              <button
                type="button"
                onClick={() => { setStep("email"); setError(null); setSuccessMessage(null); }}
                className="font-medium text-[#C9879C] hover:text-[#b5768c]"
              >
                Ganti alamat email?
              </button>
            </div>
          </form>
        )}

        <div className="text-sm text-center">
          <p>
            Belum punya akun?{" "}
            <Link
              href="/auth/register"
              className="font-medium text-[#C9879C] hover:text-[#b5768c]"
            >
              Daftar di sini
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
