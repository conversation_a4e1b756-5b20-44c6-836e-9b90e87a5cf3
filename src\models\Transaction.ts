import mongoose, { Schema, Document } from 'mongoose';

export interface ITransaction extends Document {
  userId: mongoose.Types.ObjectId;
  amount: number;
  plan: 'basic' | 'pro' | 'vip';
  status: 'pending' | 'success' | 'failed' | 'expired';
  paymentMethod: string;
  paymentDetails: {
    orderId: string;
    transactionId?: string;
    midtransToken?: string;
    paymentUrl?: string;
    paidAt?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

const TransactionSchema = new Schema<ITransaction>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  amount: { type: Number, required: true },
  plan: { 
    type: String, 
    enum: ['basic', 'pro', 'vip'], 
    required: true 
  },
  status: { 
    type: String, 
    enum: ['pending', 'success', 'failed', 'expired'], 
    default: 'pending' 
  },
  paymentMethod: { type: String, default: 'midtrans' },
  paymentDetails: {
    orderId: { type: String, required: true },
    transactionId: { type: String },
    midtransToken: { type: String },
    paymentUrl: { type: String },
    paidAt: { type: Date }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// Create indexes for efficient querying
TransactionSchema.index({ userId: 1 });
TransactionSchema.index({ 'paymentDetails.orderId': 1 }, { unique: true });
TransactionSchema.index({ status: 1, createdAt: -1 });

export default mongoose.models.Transaction || mongoose.model<ITransaction>('Transaction', TransactionSchema);
