import "next-auth/jwt";
import { DefaultSession, DefaultUser } from "next-auth";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      gender: string;
      psychTestCompleted?: boolean;
      subscription?: {
        plan: 'free' | 'basic' | 'pro' | 'vip';
        chatCreditsLeft: number;
        psychologistConsultationLeft: number;
      };
    } & DefaultSession["user"];
  }

  interface User extends DefaultUser {
    id: string;
    gender: string;
    psychTestCompleted?: boolean;
    subscription?: {
      plan: 'free' | 'basic' | 'pro' | 'vip';
      chatCreditsLeft: number;
      psychologistConsultationLeft: number;
    };
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    gender: string;
    psychTestCompleted?: boolean;
  }
}
