"use client";

import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  HelpCircle, 
  MessageCircle, 
  Mail, 
  Phone, 
  Heart, 
  Brain, 
  Users, 
  Shield,
  ChevronDown,
  ChevronRight
} from "lucide-react";
import { useState } from "react";

const faqs = [
  {
    question: "How does the psychological matching work?",
    answer: "Our matching system uses the Big Five Inventory (BFI-2-S) and Self Disclosure Test to analyze personality compatibility. We calculate similarity and complementary scores across different psychological dimensions to find your most compatible matches."
  },
  {
    question: "What is the difference between similarity and complementary matching?",
    answer: "Similarity matching pairs people with similar traits (like agreeableness and conscientiousness), while complementary matching pairs people with opposite traits that balance each other out (like extraversion and negative emotionality)."
  },
  {
    question: "How accurate are the psychological tests?",
    answer: "Our tests are based on scientifically validated psychological instruments. The BFI-2-S is a shortened version of the Big Five Inventory, widely used in psychological research. However, personality is complex and these tests provide insights rather than absolute truths."
  },
  {
    question: "Can I retake the psychological test?",
    answer: "Yes, you can retake the test anytime from your dashboard. However, we recommend waiting at least 3 months between tests as personality traits are relatively stable over time."
  },
  {
    question: "How do chat credits work?",
    answer: "Each message you send consumes 1 chat credit. Receiving messages is free. Free users get 3 credits, Basic users get 50, Pro users get 200, and VIP users get 500 credits per month."
  },
  {
    question: "What happens if I run out of chat credits?",
    answer: "You can either upgrade your subscription plan or wait for your credits to reset at the beginning of the next month. You can still receive and read messages without credits."
  },
  {
    question: "How do I report inappropriate behavior?",
    answer: "You can report users by clicking the report button in their profile or conversation. Our team reviews all reports within 24 hours and takes appropriate action."
  },
  {
    question: "Is my personal information safe?",
    answer: "Yes, we take privacy seriously. All personal data is encrypted and stored securely. We never share your information with third parties without your consent. Read our Privacy Policy for more details."
  }
];

export default function HelpPage() {
  const [openFaq, setOpenFaq] = useState<number | null>(null);

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Help & Support</h1>
          <p className="text-gray-600">
            Get help with using Pairsona and find answers to common questions
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Quick Help Cards */}
          <div className="lg:col-span-2 space-y-6">
            {/* Getting Started */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Heart className="h-5 w-5 text-[#C9879C]" />
                  <span>Getting Started</span>
                </CardTitle>
                <CardDescription>
                  New to Pairsona? Here's how to get the most out of our platform
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="h-6 w-6 bg-[#C9879C] text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                    <div>
                      <h4 className="font-medium text-gray-900">Complete Your Profile</h4>
                      <p className="text-sm text-gray-600">Add photos and fill out your basic information to make a great first impression.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="h-6 w-6 bg-[#C9879C] text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                    <div>
                      <h4 className="font-medium text-gray-900">Take the Psychological Test</h4>
                      <p className="text-sm text-gray-600">Complete our scientifically-based personality assessment for better matches.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="h-6 w-6 bg-[#C9879C] text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <div>
                      <h4 className="font-medium text-gray-900">Discover Matches</h4>
                      <p className="text-sm text-gray-600">Browse your psychologically compatible matches and start conversations.</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* FAQ Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <HelpCircle className="h-5 w-5 text-blue-600" />
                  <span>Frequently Asked Questions</span>
                </CardTitle>
                <CardDescription>
                  Find answers to the most common questions about Pairsona
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {faqs.map((faq, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg">
                      <button
                        onClick={() => toggleFaq(index)}
                        className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
                      >
                        <span className="font-medium text-gray-900">{faq.question}</span>
                        {openFaq === index ? (
                          <ChevronDown className="h-5 w-5 text-gray-500" />
                        ) : (
                          <ChevronRight className="h-5 w-5 text-gray-500" />
                        )}
                      </button>
                      {openFaq === index && (
                        <div className="px-4 pb-4">
                          <p className="text-gray-600">{faq.answer}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Features Guide */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Brain className="h-5 w-5 text-purple-600" />
                  <span>Understanding Your Matches</span>
                </CardTitle>
                <CardDescription>
                  Learn how our psychological matching system works
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900 mb-2">High Compatibility (80%+)</h4>
                    <p className="text-sm text-green-700">
                      Excellent psychological match with strong potential for long-term compatibility.
                    </p>
                  </div>
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">Good Compatibility (60-79%)</h4>
                    <p className="text-sm text-blue-700">
                      Good match with several compatible traits and balanced differences.
                    </p>
                  </div>
                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <h4 className="font-medium text-yellow-900 mb-2">Moderate Compatibility (40-59%)</h4>
                    <p className="text-sm text-yellow-700">
                      Some compatibility with potential for growth through understanding differences.
                    </p>
                  </div>
                  <div className="p-4 bg-red-50 rounded-lg">
                    <h4 className="font-medium text-red-900 mb-2">Lower Compatibility (Below 40%)</h4>
                    <p className="text-sm text-red-700">
                      Significant personality differences that may require extra effort to navigate.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Support */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MessageCircle className="h-5 w-5 text-green-600" />
                  <span>Contact Support</span>
                </CardTitle>
                <CardDescription>
                  Need more help? Get in touch with our support team
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full justify-start" variant="outline">
                  <Mail className="h-4 w-4 mr-2" />
                  <EMAIL>
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Phone className="h-4 w-4 mr-2" />
                  +62 21 1234 5678
                </Button>
                <div className="text-sm text-gray-600">
                  <p className="font-medium mb-1">Support Hours:</p>
                  <p>Monday - Friday: 9:00 AM - 6:00 PM WIB</p>
                  <p>Saturday: 10:00 AM - 4:00 PM WIB</p>
                  <p>Sunday: Closed</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-orange-600" />
                  <span>Community Guidelines</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm space-y-2">
                  <p className="font-medium text-gray-900">Be Respectful</p>
                  <p className="text-gray-600">Treat all users with kindness and respect.</p>
                  
                  <p className="font-medium text-gray-900">Be Authentic</p>
                  <p className="text-gray-600">Use real photos and honest information.</p>
                  
                  <p className="font-medium text-gray-900">Stay Safe</p>
                  <p className="text-gray-600">Never share personal information too quickly.</p>
                </div>
                <Button variant="outline" className="w-full text-sm">
                  Read Full Guidelines
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-red-600" />
                  <span>Safety & Privacy</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm space-y-2">
                  <p className="text-gray-600">
                    Your safety and privacy are our top priorities. We use industry-standard encryption and never share your data without permission.
                  </p>
                </div>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full text-sm">
                    Privacy Policy
                  </Button>
                  <Button variant="outline" className="w-full text-sm">
                    Terms of Service
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
