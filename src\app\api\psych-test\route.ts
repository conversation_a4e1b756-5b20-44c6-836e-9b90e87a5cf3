import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Pastikan path ini benar
import dbConnect from "@/lib/db/mongodb";
import PsychTest from "@/models/PsychTest";
import User from "@/models/User";

export async function POST(req: NextRequest) {
  try {
    // Get authenticated user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Parse test results from request body
    const { bfi, selfDisclosure } = await req.json();
    
    // Validate BFI data
    if (!bfi || 
        typeof bfi.extraversion !== 'number' ||
        typeof bfi.agreeableness !== 'number' ||
        typeof bfi.conscientiousness !== 'number' ||
        typeof bfi.negativeEmotionality !== 'number' ||
        typeof bfi.openMindedness !== 'number') {
      return NextResponse.json(
        { error: "Invalid BFI test data" },
        { status: 400 }
      );
    }
    
    // Validate Self Disclosure data
    if (typeof selfDisclosure !== 'number') {
      return NextResponse.json(
        { error: "Invalid Self Disclosure test data" },
        { status: 400 }
      );
    }
    
    // Validate all scores are between 0 and 1
    const scores = [
      bfi.extraversion,
      bfi.agreeableness,
      bfi.conscientiousness,
      bfi.negativeEmotionality,
      bfi.openMindedness,
      selfDisclosure
    ];
    
    if (scores.some(score => score < 0 || score > 1)) {
      return NextResponse.json(
        { error: "All scores must be between 0 and 1" },
        { status: 400 }
      );
    }
    
    // Connect to database
    await dbConnect();
    
    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // Create or update psych test results
    const testData = {
      userId,
      bfi: {
        extraversion: bfi.extraversion,
        agreeableness: bfi.agreeableness,
        conscientiousness: bfi.conscientiousness,
        negativeEmotionality: bfi.negativeEmotionality,
        openMindedness: bfi.openMindedness
      },
      selfDisclosure
    };
    
    // Use findOneAndUpdate with upsert to create or update
    await PsychTest.findOneAndUpdate(
      { userId },
      testData,
      { upsert: true, new: true }
    );
    
    // Update user to mark psych test as completed
    await User.findByIdAndUpdate(userId, { psychTestCompleted: true });
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: "Psychological test results saved successfully"
    });
    
  } catch (error) {
    console.error("Psych test error:", error);
    return NextResponse.json(
      { error: "Failed to save psychological test results" },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Get authenticated user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    
    // Connect to database
    await dbConnect();
    
    // Find user's psych test results
    const psychTest = await PsychTest.findOne({ userId });
    
    if (!psychTest) {
      return NextResponse.json(
        { error: "Psychological test results not found" },
        { status: 404 }
      );
    }
    
    // Return test results
    return NextResponse.json({
      success: true,
      data: {
        bfi: psychTest.bfi,
        selfDisclosure: psychTest.selfDisclosure
      }
    });
    
  } catch (error) {
    console.error("Psych test retrieval error:", error);
    return NextResponse.json(
      { error: "Failed to retrieve psychological test results" },
      { status: 500 }
    );
  }
}
