import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/db/mongodb";
import User from "@/models/User";
import PsychTest from "@/models/PsychTest";
import bcrypt from "bcryptjs";

export async function POST(req: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: "This endpoint is only available in development" },
        { status: 403 }
      );
    }

    await dbConnect();

    // Create dummy users
    const dummyUsers = [
      {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: await bcrypt.hash("password123", 10),
        gender: "female",
        dateOfBirth: new Date("1995-03-15"),
        occupation: "Software Engineer",
        religion: "Christian",
        about: "I love hiking, reading, and exploring new technologies. Looking for someone who shares my passion for adventure and learning.",
        photos: [],
        psychTestCompleted: true,
        emailVerified: new Date(),
        subscription: {
          plan: 'free',
          chatCreditsLeft: 3,
          psychologistConsultationLeft: 0
        }
      },
      {
        name: "<PERSON>",
        email: "<EMAIL>", 
        password: await bcrypt.hash("password123", 10),
        gender: "female",
        dateOfBirth: new Date("1992-08-22"),
        occupation: "Graphic Designer",
        religion: "Buddhist",
        about: "Creative soul who loves art, meditation, and good coffee. Seeking meaningful connections and deep conversations.",
        photos: [],
        psychTestCompleted: true,
        emailVerified: new Date(),
        subscription: {
          plan: 'free',
          chatCreditsLeft: 3,
          psychologistConsultationLeft: 0
        }
      },
      {
        name: "Maria Rodriguez",
        email: "<EMAIL>",
        password: await bcrypt.hash("password123", 10),
        gender: "female",
        dateOfBirth: new Date("1990-12-05"),
        occupation: "Doctor",
        religion: "Catholic",
        about: "Passionate about helping others and making a difference. Love traveling, cooking, and spending time with family.",
        photos: [],
        psychTestCompleted: true,
        emailVerified: new Date(),
        subscription: {
          plan: 'basic',
          chatCreditsLeft: 50,
          psychologistConsultationLeft: 0
        }
      },
      {
        name: "Alex Thompson",
        email: "<EMAIL>",
        password: await bcrypt.hash("password123", 10),
        gender: "male",
        dateOfBirth: new Date("1988-06-10"),
        occupation: "Marketing Manager",
        religion: "Agnostic",
        about: "Outgoing person who loves sports, music, and trying new restaurants. Looking for someone to share life's adventures with.",
        photos: [],
        psychTestCompleted: true,
        emailVerified: new Date(),
        subscription: {
          plan: 'free',
          chatCreditsLeft: 3,
          psychologistConsultationLeft: 0
        }
      },
      {
        name: "David Kim",
        email: "<EMAIL>",
        password: await bcrypt.hash("password123", 10),
        gender: "male",
        dateOfBirth: new Date("1993-11-18"),
        occupation: "Teacher",
        religion: "Christian",
        about: "Educator who believes in lifelong learning. Enjoy reading, playing guitar, and volunteering in the community.",
        photos: [],
        psychTestCompleted: true,
        emailVerified: new Date(),
        subscription: {
          plan: 'free',
          chatCreditsLeft: 3,
          psychologistConsultationLeft: 0
        }
      }
    ];

    // Clear existing dummy users
    await User.deleteMany({ email: { $in: dummyUsers.map(u => u.email) } });

    // Create users
    const createdUsers = await User.insertMany(dummyUsers);

    // Create psychological test results for each user
    const psychTests = createdUsers.map(user => ({
      userId: user._id,
      bfi: {
        extraversion: Math.random() * 0.6 + 0.2, // 0.2 to 0.8
        agreeableness: Math.random() * 0.6 + 0.2,
        conscientiousness: Math.random() * 0.6 + 0.2,
        negativeEmotionality: Math.random() * 0.6 + 0.2,
        openMindedness: Math.random() * 0.6 + 0.2
      },
      selfDisclosure: Math.random() * 0.6 + 0.2
    }));

    // Clear existing psych tests for these users
    await PsychTest.deleteMany({ userId: { $in: createdUsers.map(u => u._id) } });

    // Create psych tests
    await PsychTest.insertMany(psychTests);

    return NextResponse.json({
      success: true,
      message: `Created ${createdUsers.length} dummy users with psychological test results`,
      data: {
        users: createdUsers.map(u => ({
          id: u._id,
          name: u.name,
          email: u.email,
          gender: u.gender
        }))
      }
    });

  } catch (error) {
    console.error("Seed data error:", error);
    return NextResponse.json(
      { error: "Failed to create seed data" },
      { status: 500 }
    );
  }
}
