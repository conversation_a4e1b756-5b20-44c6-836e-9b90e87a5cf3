import mongoose, { Schema, Document } from 'mongoose';

export interface IMatch extends Document {
  user1Id: mongoose.Types.ObjectId;
  user2Id: mongoose.Types.ObjectId;
  matchScore: number; // 0-1 scale
  matchDetails: {
    extraversion: number;
    agreeableness: number;
    conscientiousness: number;
    negativeEmotionality: number;
    openMindedness: number;
    selfDisclosure: number;
  };
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  initiatedBy: mongoose.Types.ObjectId | null;
  createdAt: Date;
  updatedAt: Date;
}

const MatchSchema = new Schema<IMatch>({
  user1Id: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  user2Id: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  matchScore: { type: Number, required: true, min: 0, max: 1 },
  matchDetails: {
    extraversion: { type: Number, required: true, min: 0, max: 1 },
    agreeableness: { type: Number, required: true, min: 0, max: 1 },
    conscientiousness: { type: Number, required: true, min: 0, max: 1 },
    negativeEmotionality: { type: Number, required: true, min: 0, max: 1 },
    openMindedness: { type: Number, required: true, min: 0, max: 1 },
    selfDisclosure: { type: Number, required: true, min: 0, max: 1 }
  },
  status: { 
    type: String, 
    enum: ['pending', 'accepted', 'rejected', 'expired'], 
    default: 'pending' 
  },
  initiatedBy: { type: Schema.Types.ObjectId, ref: 'User', default: null },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// Create a compound index to ensure unique pairs
MatchSchema.index({ user1Id: 1, user2Id: 1 }, { unique: true });

// Create indexes for efficient querying
MatchSchema.index({ user1Id: 1, status: 1 });
MatchSchema.index({ user2Id: 1, status: 1 });
MatchSchema.index({ matchScore: -1 }); // For sorting by match score

export default mongoose.models.Match || mongoose.model<IMatch>('Match', MatchSchema);
