import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/db/mongodb";
import Transaction from "@/models/Transaction";
import User from "@/models/User";

// Mock function for Midtrans notification verification (replace with actual Midtrans API integration)
async function verifyMidtransNotification(notification: any) {
  // In production, this would verify the signature and authenticity of the Midtrans notification
  console.log(`Verifying Midtrans notification for order ${notification.order_id}`);
  
  // For demo purposes, assume all notifications are valid
  return {
    isValid: true,
    orderId: notification.order_id,
    transactionStatus: notification.transaction_status,
    fraudStatus: notification.fraud_status
  };
}

export async function POST(req: NextRequest) {
  try {
    // Parse notification from Midtrans
    const notification = await req.json();
    
    // Verify notification
    const verificationResult = await verifyMidtransNotification(notification);
    if (!verificationResult.isValid) {
      return NextResponse.json(
        { error: "Invalid notification" },
        { status: 400 }
      );
    }
    
    // Connect to database
    await dbConnect();
    
    // Find transaction by order ID
    const transaction = await Transaction.findOne({
      'paymentDetails.orderId': verificationResult.orderId
    });
    
    if (!transaction) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      );
    }
    
    // Determine transaction status
    let status = 'pending';
    if (verificationResult.transactionStatus === 'capture' || verificationResult.transactionStatus === 'settlement') {
      if (verificationResult.fraudStatus === 'accept') {
        status = 'success';
      }
    } else if (verificationResult.transactionStatus === 'cancel' || 
               verificationResult.transactionStatus === 'deny' || 
               verificationResult.transactionStatus === 'expire') {
      status = 'failed';
    }
    
    // Update transaction status
    transaction.status = status;
    transaction.paymentDetails.transactionId = notification.transaction_id || null;
    transaction.paymentDetails.paidAt = status === 'success' ? new Date() : null;
    await transaction.save();
    
    // If payment is successful, update user subscription
    if (status === 'success') {
      // Get subscription plan details
      const subscriptionPlans = {
        basic: {
          duration: 30, // days
          chatCredits: 50,
          psychologistConsultation: 0
        },
        pro: {
          duration: 30, // days
          chatCredits: 200,
          psychologistConsultation: 1
        },
        vip: {
          duration: 30, // days
          chatCredits: 500,
          psychologistConsultation: 3
        }
      };
      
      const plan = transaction.plan as keyof typeof subscriptionPlans;
      const planDetails = subscriptionPlans[plan];
      
      if (!planDetails) {
        return NextResponse.json(
          { error: "Invalid subscription plan" },
          { status: 400 }
        );
      }
      
      // Calculate subscription dates
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + planDetails.duration);
      
      // Update user subscription
      await User.findByIdAndUpdate(transaction.userId, {
        'subscription.plan': plan,
        'subscription.startDate': startDate,
        'subscription.endDate': endDate,
        'subscription.chatCreditsLeft': planDetails.chatCredits,
        'subscription.psychologistConsultationLeft': planDetails.psychologistConsultation
      });
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: `Transaction ${status}`,
      data: {
        orderId: verificationResult.orderId,
        status
      }
    });
    
  } catch (error) {
    console.error("Payment notification error:", error);
    return NextResponse.json(
      { error: "Failed to process payment notification" },
      { status: 500 }
    );
  }
}
