import mongoose, { Schema, Document } from 'mongoose';

export interface IPsychTest extends Document {
  userId: mongoose.Types.ObjectId;
  bfi: {
    extraversion: number; // 0-1 scale
    agreeableness: number;
    conscientiousness: number;
    negativeEmotionality: number;
    openMindedness: number;
  };
  selfDisclosure: number; // 0-1 scale
  createdAt: Date;
  updatedAt: Date;
}

const PsychTestSchema = new Schema<IPsychTest>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  bfi: {
    extraversion: { type: Number, required: true, min: 0, max: 1 },
    agreeableness: { type: Number, required: true, min: 0, max: 1 },
    conscientiousness: { type: Number, required: true, min: 0, max: 1 },
    negativeEmotionality: { type: Number, required: true, min: 0, max: 1 },
    openMindedness: { type: Number, required: true, min: 0, max: 1 }
  },
  selfDisclosure: { type: Number, required: true, min: 0, max: 1 },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// Create a unique index on userId to ensure one test result per user
PsychTestSchema.index({ userId: 1 }, { unique: true });

export default mongoose.models.PsychTest || mongoose.model<IPsychTest>('PsychTest', PsychTestSchema);
