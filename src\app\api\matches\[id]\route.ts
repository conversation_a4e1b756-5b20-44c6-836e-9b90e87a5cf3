import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import dbConnect from "@/lib/db/mongodb";
import Match from "@/models/Match";
import User from "@/models/User";
import Conversation from "@/models/Conversation";

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get authenticated user session
    const session = await getServerSession();
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const matchId = params.id;
    
    // Connect to database
    await dbConnect();
    
    // Find match by ID
    const match = await Match.findById(matchId)
      .populate('user1Id', 'name image photos gender dateOfBirth occupation about religion isSmoker acceptDifferentReligion')
      .populate('user2Id', 'name image photos gender dateOfBirth occupation about religion isSmoker acceptDifferentReligion');
    
    if (!match) {
      return NextResponse.json(
        { error: "Match not found" },
        { status: 404 }
      );
    }
    
    // Verify that the requesting user is part of this match
    if (match.user1Id._id.toString() !== userId && match.user2Id._id.toString() !== userId) {
      return NextResponse.json(
        { error: "You are not authorized to view this match" },
        { status: 403 }
      );
    }
    
    // Determine which user is the current user and which is the other user
    const isUser1 = match.user1Id._id.toString() === userId;
    const currentUser = isUser1 ? match.user1Id : match.user2Id;
    const otherUser = isUser1 ? match.user2Id : match.user1Id;
    
    // Format match data for response
    const matchData = {
      matchId: match._id,
      matchScore: match.matchScore,
      matchDetails: match.matchDetails,
      status: match.status,
      initiatedBy: match.initiatedBy ? match.initiatedBy.toString() : null,
      createdAt: match.createdAt,
      otherUser: {
        id: otherUser._id,
        name: otherUser.name,
        image: otherUser.image,
        photos: otherUser.photos,
        gender: otherUser.gender,
        dateOfBirth: otherUser.dateOfBirth,
        age: otherUser.dateOfBirth ? 
          Math.floor((new Date().getTime() - new Date(otherUser.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : 
          null,
        occupation: otherUser.occupation,
        about: otherUser.about,
        religion: otherUser.religion,
        isSmoker: otherUser.isSmoker,
        acceptDifferentReligion: otherUser.acceptDifferentReligion
      }
    };
    
    return NextResponse.json({
      success: true,
      data: matchData
    });
    
  } catch (error) {
    console.error("Match retrieval error:", error);
    return NextResponse.json(
      { error: "Failed to retrieve match" },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get authenticated user session
    const session = await getServerSession();
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    const matchId = params.id;
    
    // Parse request body
    const { action } = await req.json();
    
    // Validate action
    if (!action || !['accept', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: "Invalid action. Must be 'accept' or 'reject'" },
        { status: 400 }
      );
    }
    
    // Connect to database
    await dbConnect();
    
    // Find match by ID
    const match = await Match.findById(matchId);
    
    if (!match) {
      return NextResponse.json(
        { error: "Match not found" },
        { status: 404 }
      );
    }
    
    // Verify that the requesting user is part of this match
    if (match.user1Id.toString() !== userId && match.user2Id.toString() !== userId) {
      return NextResponse.json(
        { error: "You are not authorized to update this match" },
        { status: 403 }
      );
    }
    
    // Update match status
    match.status = action === 'accept' ? 'accepted' : 'rejected';
    match.initiatedBy = userId;
    await match.save();
    
    // If match is accepted, create a conversation
    if (action === 'accept') {
      // Check if conversation already exists
      const existingConversation = await Conversation.findOne({ matchId });
      
      if (!existingConversation) {
        // Create new conversation
        const conversation = new Conversation({
          participants: [match.user1Id, match.user2Id],
          matchId: match._id,
          messages: []
        });
        
        await conversation.save();
      }
      
      // Check if user has enough chat credits
      const user = await User.findById(userId);
      if (!user) {
        return NextResponse.json(
          { error: "User not found" },
          { status: 404 }
        );
      }
      
      if (user.subscription.chatCreditsLeft <= 0) {
        return NextResponse.json({
          success: true,
          message: "Match accepted but you have no chat credits left. Please upgrade your subscription.",
          data: { match: { ...match.toObject(), status: 'accepted' } }
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Match ${action === 'accept' ? 'accepted' : 'rejected'} successfully`,
      data: { match: match.toObject() }
    });
    
  } catch (error) {
    console.error("Match update error:", error);
    return NextResponse.json(
      { error: "Failed to update match" },
      { status: 500 }
    );
  }
}
